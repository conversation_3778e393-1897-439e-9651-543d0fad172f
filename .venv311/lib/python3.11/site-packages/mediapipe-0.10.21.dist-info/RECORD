mediapipe-0.10.21.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
mediapipe-0.10.21.dist-info/LICENSE,sha256=hwfu8FM5h-_FsVXWR2HutuIHk_ULm9Gmja0c9HGdDtg,12331
mediapipe-0.10.21.dist-info/METADATA,sha256=_H9rkszfZjjXFjYKOKs5iiWjGU2KHShxo56d8ArHLeE,9910
mediapipe-0.10.21.dist-info/RECORD,,
mediapipe-0.10.21.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe-0.10.21.dist-info/WHEEL,sha256=Y9xBhY_XP3xwJrOrIUfu7QlpS6j5_pe9XMG4igKe0bw,137
mediapipe-0.10.21.dist-info/top_level.txt,sha256=LG-epD1oIiiHFRqLp--7jacjB3dbx2RfMcLYjCIhmxU,175
mediapipe/__init__.py,sha256=W2xM191Zi5r77CJmaRRkH0sg7bV1IPT9Y3JLCRK5iVM,807
mediapipe/__pycache__/__init__.cpython-311.pyc,,
mediapipe/calculators/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/calculators/__pycache__/__init__.cpython-311.pyc,,
mediapipe/calculators/audio/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/calculators/audio/__pycache__/__init__.cpython-311.pyc,,
mediapipe/calculators/audio/__pycache__/mfcc_mel_calculators_pb2.cpython-311.pyc,,
mediapipe/calculators/audio/__pycache__/rational_factor_resample_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/audio/__pycache__/resample_time_series_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/audio/__pycache__/spectrogram_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/audio/__pycache__/stabilized_log_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/audio/__pycache__/time_series_framer_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/audio/mfcc_mel_calculators_pb2.py,sha256=ztuxslhlHa6yvgQUpCsparcvpOT0CZK8G0OE56slyP0,2355
mediapipe/calculators/audio/rational_factor_resample_calculator_pb2.py,sha256=zaH3wPpsEOG8ir9YXwzQcJcbBUIqBWC5QD7wscAMOIY,2488
mediapipe/calculators/audio/resample_time_series_calculator_pb2.py,sha256=1FB7qBJXuOJPagf9CVFjt-ixihDGns0BvpxwpBBQZBQ,3563
mediapipe/calculators/audio/spectrogram_calculator_pb2.py,sha256=DcGqVO20Qf-iy2ehCDNqofJlcgvNmkS-Zyhf23_oRo0,3409
mediapipe/calculators/audio/stabilized_log_calculator_pb2.py,sha256=g_Y-u893HrVDV1EEkRqIPjEOs48omMpksPbtQh73ki4,1942
mediapipe/calculators/audio/time_series_framer_calculator_pb2.py,sha256=NAtab-Lbas1FhhElNpNeuh2UiTAVyIUTtcDu_by6-UM,2500
mediapipe/calculators/core/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/calculators/core/__pycache__/__init__.cpython-311.pyc,,
mediapipe/calculators/core/__pycache__/bypass_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/core/__pycache__/clip_vector_size_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/core/__pycache__/concatenate_vector_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/core/__pycache__/constant_side_packet_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/core/__pycache__/dequantize_byte_array_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/core/__pycache__/flow_limiter_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/core/__pycache__/gate_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/core/__pycache__/get_vector_item_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/core/__pycache__/graph_profile_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/core/__pycache__/packet_cloner_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/core/__pycache__/packet_resampler_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/core/__pycache__/packet_thinner_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/core/__pycache__/quantize_float_vector_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/core/__pycache__/sequence_shift_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/core/__pycache__/split_vector_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/core/bypass_calculator_pb2.py,sha256=PLRyzH3oxGiD8Giyrdd0dsZSro0wgGaHjNkA34iZHrA,1818
mediapipe/calculators/core/clip_vector_size_calculator_pb2.py,sha256=OH8RgP_ouLZawyRvJlD_DgLs0kos8F-gyWlL1XBEWio,1840
mediapipe/calculators/core/concatenate_vector_calculator_pb2.py,sha256=au2iK8MiEpT-YNWmprkAYdzsiUJghMvV561aO_SOuCU,1860
mediapipe/calculators/core/constant_side_packet_calculator_pb2.py,sha256=FnhqHzb-Mox9P3rS1MiY0uCF4DfoUtnh1KI9MGchzPE,3896
mediapipe/calculators/core/dequantize_byte_array_calculator_pb2.py,sha256=fi_7wW9dk-d6_46kHRYMMMH3LSYc67oihfrsPVOYRtk,1918
mediapipe/calculators/core/flow_limiter_calculator_pb2.py,sha256=ui-CwKLN64N9P6w9zntFL52bG4FNOwi4UDGyzMcAG38,2137
mediapipe/calculators/core/gate_calculator_pb2.py,sha256=qzRDXg2llQVwyWfXAwF45B6mbY2yLSs7Y23BxfVLPNY,2219
mediapipe/calculators/core/get_vector_item_calculator_pb2.py,sha256=KINjItYr-1wPV_7130PtRXS5TcitMYPpjXJIY3EhKr4,1868
mediapipe/calculators/core/graph_profile_calculator_pb2.py,sha256=YFcwn-CBoRprIWMq7vb-eWWGwGB_cX8Hi5D3HuBwvEw,1845
mediapipe/calculators/core/packet_cloner_calculator_pb2.py,sha256=Po058HY3ylU3zxpPUafDtC38zRkl6TSy7HoePhb7W_c,1924
mediapipe/calculators/core/packet_resampler_calculator_pb2.py,sha256=7hK2vlB3hcc6K7gdLovg8MVkvtlTWEFyYMkIf5CxMKQ,2764
mediapipe/calculators/core/packet_thinner_calculator_pb2.py,sha256=NqUzjaamAHsDl4cYzF_PcHWjw8OWCGO7oCc42Agn14g,2374
mediapipe/calculators/core/quantize_float_vector_calculator_pb2.py,sha256=wwANXp4HSJoeQ7LOwi11AMcl-RwbmU3DeIc0woGvPDw,1911
mediapipe/calculators/core/sequence_shift_calculator_pb2.py,sha256=TpaioxJ75m_oOA7zQ8yqEVhSOpPECzH2d_SvH_woGmA,1900
mediapipe/calculators/core/split_vector_calculator_pb2.py,sha256=w8CSR8gy88fVoxT4NYfhuohc7vCDrEUCUcHTeIiLvps,2116
mediapipe/calculators/image/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/calculators/image/__pycache__/__init__.cpython-311.pyc,,
mediapipe/calculators/image/__pycache__/bilateral_filter_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/image/__pycache__/feature_detector_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/image/__pycache__/image_clone_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/image/__pycache__/image_cropping_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/image/__pycache__/image_transformation_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/image/__pycache__/mask_overlay_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/image/__pycache__/opencv_encoded_image_to_image_frame_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/image/__pycache__/opencv_image_encoder_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/image/__pycache__/recolor_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/image/__pycache__/rotation_mode_pb2.cpython-311.pyc,,
mediapipe/calculators/image/__pycache__/scale_image_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/image/__pycache__/segmentation_smoothing_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/image/__pycache__/set_alpha_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/image/__pycache__/warp_affine_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/image/bilateral_filter_calculator_pb2.py,sha256=XDHwCyRamTBsnqs_Hn2DvY-SLKM9BlblrwFrOWPVoJ0,1867
mediapipe/calculators/image/feature_detector_calculator_pb2.py,sha256=R10On6hF2bLwPpiI0JDUnZiQZa323mjQKEQeNFbaf84,1997
mediapipe/calculators/image/image_clone_calculator_pb2.py,sha256=hl-bzZxwyFSwz10jRYzxzjLXNTgmk0mGi9Nc6Jsh_vM,1812
mediapipe/calculators/image/image_cropping_calculator_pb2.py,sha256=tTEYtHyr2f1LQs3gSY_PiuIT_bvvgbGNq0NP4b2nD-s,2587
mediapipe/calculators/image/image_transformation_calculator_pb2.py,sha256=ORthtlHJNB05v7pGw6jp3baFEHNcws4DegIcpI_yhC8,3617
mediapipe/calculators/image/mask_overlay_calculator_pb2.py,sha256=QzC6ifS6ZUL8HwpwnWlCuY3tgy2qjb5SDFCSHf7qaCk,2121
mediapipe/calculators/image/opencv_encoded_image_to_image_frame_calculator_pb2.py,sha256=jff3w0XUErkXp6n0UF5o2IqUiOC5dnMYhSztNWH7b_4,1976
mediapipe/calculators/image/opencv_image_encoder_calculator_pb2.py,sha256=kHQ_AaYEcQ8k7_tciHRgluZAJnlBtHIo2q-9bYwTR_M,2519
mediapipe/calculators/image/recolor_calculator_pb2.py,sha256=9DT_zioDUjAepAlhSA6LH6hwe-O76Q06lCVXLGtG3Dk,2370
mediapipe/calculators/image/rotation_mode_pb2.py,sha256=c8bAEzFcncEpcfqGJVAaaVD8MVC-4YDBY_x21ASGdO4,1557
mediapipe/calculators/image/scale_image_calculator_pb2.py,sha256=oR1nCyTnITKxJO6CkyLDSugrYGzeEesXo30YaRZdjyw,3287
mediapipe/calculators/image/segmentation_smoothing_calculator_pb2.py,sha256=6_Q62bhXxggL1DYAoVFAk7kHY2anwOkJ9eXpZBhkvKM,1901
mediapipe/calculators/image/set_alpha_calculator_pb2.py,sha256=zWFrsUl1rv4IOwsHz5QMMSRIU2GVZQEvzkIRieJo6nI,1785
mediapipe/calculators/image/warp_affine_calculator_pb2.py,sha256=okVzxgYvm2AD1MKpq0jfXeeSNh72k7C-VC8651bosFM,2719
mediapipe/calculators/internal/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/calculators/internal/__pycache__/__init__.cpython-311.pyc,,
mediapipe/calculators/internal/__pycache__/callback_packet_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/internal/callback_packet_calculator_pb2.py,sha256=2BjvY7lCb5ppRz545ngBg5QbolsW2mnUJKXuM6DYLZw,2211
mediapipe/calculators/tensor/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/calculators/tensor/__pycache__/__init__.cpython-311.pyc,,
mediapipe/calculators/tensor/__pycache__/audio_to_tensor_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/tensor/__pycache__/bert_preprocessor_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/tensor/__pycache__/feedback_tensors_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/tensor/__pycache__/image_to_tensor_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/tensor/__pycache__/inference_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/tensor/__pycache__/landmarks_to_tensor_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/tensor/__pycache__/regex_preprocessor_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/tensor/__pycache__/tensor_converter_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/tensor/__pycache__/tensor_to_joints_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/tensor/__pycache__/tensors_readback_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/tensor/__pycache__/tensors_to_audio_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/tensor/__pycache__/tensors_to_classification_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/tensor/__pycache__/tensors_to_detections_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/tensor/__pycache__/tensors_to_floats_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/tensor/__pycache__/tensors_to_landmarks_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/tensor/__pycache__/tensors_to_segmentation_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/tensor/__pycache__/vector_to_tensor_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/tensor/audio_to_tensor_calculator_pb2.py,sha256=q9H8Ic-jUJmJOGft7N-F8FEGE4iw56SEe4pfYx3jbyY,3274
mediapipe/calculators/tensor/bert_preprocessor_calculator_pb2.py,sha256=oe6JNS5ztBDk4eGpXGuIoxwYDb9z6-v3Ec0rul3zgvg,1906
mediapipe/calculators/tensor/feedback_tensors_calculator_pb2.py,sha256=lD-6LcCX-cxGcM5pFeQ20pAr3f11cjb26haT9v9DcUQ,2897
mediapipe/calculators/tensor/image_to_tensor_calculator_pb2.py,sha256=mGnzmMvPTjXXb4G6EwUd-JcwSw0ocbhmcTW-t1WdCwk,3617
mediapipe/calculators/tensor/inference_calculator_pb2.py,sha256=JVbqIaLoRceTxKHrrFbglYoZLQsFqpKsri_i-CDSggk,8253
mediapipe/calculators/tensor/landmarks_to_tensor_calculator_pb2.py,sha256=6KmPNBQRI7wjqFo3huw2xVXZAf2XIcHoQrce4owW-3g,2275
mediapipe/calculators/tensor/regex_preprocessor_calculator_pb2.py,sha256=0m4rnQwRP8tCzcphGkwn8jBQ9VNQXIGyfL7UzHZc8Kw,1850
mediapipe/calculators/tensor/tensor_converter_calculator_pb2.py,sha256=upS0Rh_0_9wI77DxenHnAVARzHuFkZZqRONdNiQc64w,2835
mediapipe/calculators/tensor/tensor_to_joints_calculator_pb2.py,sha256=zwrNmpruk5L5Y8XxWL6BY2CVPSwqViZGjyNWwEP2zm4,1882
mediapipe/calculators/tensor/tensors_readback_calculator_pb2.py,sha256=551JGYMrZkKNbIJ0x89qKFA4D58JngFhLPPHczwQ5No,2353
mediapipe/calculators/tensor/tensors_to_audio_calculator_pb2.py,sha256=HeBl_tROb3ufyVFj00ETzfPuiT6DRc7ET9Qovwt3A9k,2492
mediapipe/calculators/tensor/tensors_to_classification_calculator_pb2.py,sha256=Y06W-hgpsf55zbF9Zq0doWNYw60MlyPYfhIzpudrEc4,4101
mediapipe/calculators/tensor/tensors_to_detections_calculator_pb2.py,sha256=IAe7CnVixrPTewkFLS6P6mRq9-QPjqkBKKJg9ZTKBZ0,4730
mediapipe/calculators/tensor/tensors_to_floats_calculator_pb2.py,sha256=im4uq6tlev6jKD_qxsVufREhwZysWxxWaHMw8IICnvw,2134
mediapipe/calculators/tensor/tensors_to_landmarks_calculator_pb2.py,sha256=_cuHvpN0fcus9FYH4eVf87j4R78DhDznWdR8eTn9bq4,2625
mediapipe/calculators/tensor/tensors_to_segmentation_calculator_pb2.py,sha256=CrApt-t6vFcRfpIgf1hopO2HLd6ME9CIJ_E_ymfbeAE,2473
mediapipe/calculators/tensor/vector_to_tensor_calculator_pb2.py,sha256=dx-mFSlyoPy3K-_Baqo12NCRuiAcxsu4q0S71DAl-A8,1468
mediapipe/calculators/tflite/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/calculators/tflite/__pycache__/__init__.cpython-311.pyc,,
mediapipe/calculators/tflite/__pycache__/ssd_anchors_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/tflite/__pycache__/tflite_converter_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/tflite/__pycache__/tflite_custom_op_resolver_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/tflite/__pycache__/tflite_inference_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/tflite/__pycache__/tflite_tensors_to_classification_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/tflite/__pycache__/tflite_tensors_to_detections_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/tflite/__pycache__/tflite_tensors_to_landmarks_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/tflite/__pycache__/tflite_tensors_to_segmentation_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/tflite/ssd_anchors_calculator_pb2.py,sha256=gsiNNkp-gcT4lw5WeZGyL8UVHC62peFMGikiV1czZVM,3103
mediapipe/calculators/tflite/tflite_converter_calculator_pb2.py,sha256=Z4X-28MSSUr0feTcdFxdt202cDjyDruJDg5qBbFgEpI,2639
mediapipe/calculators/tflite/tflite_custom_op_resolver_calculator_pb2.py,sha256=7wzbQ4-SYb5iK0CHtbcZToMKxt-08Cp8p4TtBBqpiI0,1895
mediapipe/calculators/tflite/tflite_inference_calculator_pb2.py,sha256=lLJtJSRJChOcEWu_Hefsz4rzpwHO3elY6KWjaXT2P7o,5098
mediapipe/calculators/tflite/tflite_tensors_to_classification_calculator_pb2.py,sha256=7-bp2sAm1EeWQfHlx8fibPd768N1sHBlzCyn62gpal4,2080
mediapipe/calculators/tflite/tflite_tensors_to_detections_calculator_pb2.py,sha256=mwNbRvQUh1pGRxCMq2sp7gfIAP5OJrnecIxb5Kh5oAA,2815
mediapipe/calculators/tflite/tflite_tensors_to_landmarks_calculator_pb2.py,sha256=agmtZIoVuom5YMDp19voqcOZq7Pn4TYDO7T8XIA8bJ4,2692
mediapipe/calculators/tflite/tflite_tensors_to_segmentation_calculator_pb2.py,sha256=NasaoswbeHqabW4WeBLPDeMYuvNN7ts5-RGmUGcwNFk,2191
mediapipe/calculators/util/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/calculators/util/__pycache__/__init__.cpython-311.pyc,,
mediapipe/calculators/util/__pycache__/align_hand_to_pose_in_world_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/util/__pycache__/annotation_overlay_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/util/__pycache__/association_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/util/__pycache__/collection_has_min_size_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/util/__pycache__/combine_joints_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/util/__pycache__/detection_label_id_to_text_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/util/__pycache__/detections_to_rects_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/util/__pycache__/detections_to_render_data_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/util/__pycache__/face_to_rect_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/util/__pycache__/filter_detections_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/util/__pycache__/flat_color_image_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/util/__pycache__/labels_to_render_data_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/util/__pycache__/landmark_projection_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/util/__pycache__/landmarks_refinement_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/util/__pycache__/landmarks_smoothing_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/util/__pycache__/landmarks_to_detection_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/util/__pycache__/landmarks_to_floats_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/util/__pycache__/landmarks_to_render_data_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/util/__pycache__/landmarks_transformation_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/util/__pycache__/latency_pb2.cpython-311.pyc,,
mediapipe/calculators/util/__pycache__/local_file_contents_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/util/__pycache__/logic_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/util/__pycache__/non_max_suppression_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/util/__pycache__/packet_frequency_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/util/__pycache__/packet_frequency_pb2.cpython-311.pyc,,
mediapipe/calculators/util/__pycache__/packet_latency_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/util/__pycache__/rect_to_render_data_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/util/__pycache__/rect_to_render_scale_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/util/__pycache__/rect_transformation_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/util/__pycache__/refine_landmarks_from_heatmap_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/util/__pycache__/resource_provider_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/util/__pycache__/set_joints_visibility_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/util/__pycache__/thresholding_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/util/__pycache__/timed_box_list_id_to_label_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/util/__pycache__/timed_box_list_to_render_data_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/util/__pycache__/top_k_scores_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/util/__pycache__/visibility_copy_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/util/__pycache__/visibility_smoothing_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/util/align_hand_to_pose_in_world_calculator_pb2.py,sha256=QQKW0LzNj3dqj9P18FLOzGfhZnJ1RvWsWCd8b3Nm0AY,1945
mediapipe/calculators/util/annotation_overlay_calculator_pb2.py,sha256=QGDlqfzYnJtPNfHU2F3_yjXlGpHFyLhRY10lVKjNNIg,2308
mediapipe/calculators/util/association_calculator_pb2.py,sha256=oBY703pABISJSGO_YXmHf-iJJ75pCiEWvzdaiz5MBNc,1823
mediapipe/calculators/util/collection_has_min_size_calculator_pb2.py,sha256=ySGUd2EeiCydrg97yveNQTmRKszAoPq3nKyX5CRGKBE,1871
mediapipe/calculators/util/combine_joints_calculator_pb2.py,sha256=NYQjpdbVK7fYOXWINZl2MVwvE2GqTMtukfGYS6g5McM,2595
mediapipe/calculators/util/detection_label_id_to_text_calculator_pb2.py,sha256=1PvM5iK1nX5JQbNFqyrlhZOd8UT-3RqJTRDVOV89L0c,2707
mediapipe/calculators/util/detections_to_rects_calculator_pb2.py,sha256=032N3-T8vOL4fwEPaTJmltI4LP2HX2JSgdYc5OeQyAM,2523
mediapipe/calculators/util/detections_to_render_data_calculator_pb2.py,sha256=KvqyUCxEczgzT3ozHZ0QSQtEqvExtPTO0le96LkTHk0,2538
mediapipe/calculators/util/face_to_rect_calculator_pb2.py,sha256=BR6g78g-rtRh5CkmTsVYHUZ7N-jH3vc--UK2O4Zb0NI,1498
mediapipe/calculators/util/filter_detections_calculator_pb2.py,sha256=GjrzT4ZmowauATdVjLMhc-IQ3CCspibMgmiUm7L9Rik,1923
mediapipe/calculators/util/flat_color_image_calculator_pb2.py,sha256=JcJOJgLb_QW2DmiDDXA6JK0YPKjS_1jhg-ggF6byZRw,2043
mediapipe/calculators/util/labels_to_render_data_calculator_pb2.py,sha256=JNAMVhCk4lMdcpfOXY8HR44kndiWEZm7Mp9svstFyEM,2925
mediapipe/calculators/util/landmark_projection_calculator_pb2.py,sha256=o5Py07qr2NxuSPKUQ5WzXDqxPfhbw1U3SvUH9pQuR4s,1860
mediapipe/calculators/util/landmarks_refinement_calculator_pb2.py,sha256=26Un-FGf0uMvGcBtvCl2fKvUjCrXcqrlkFDkYGDsVv0,3527
mediapipe/calculators/util/landmarks_smoothing_calculator_pb2.py,sha256=4chNoRI_EJMTiIVmcejlDdvFP3dDQuSD0bg08xvRAU4,3055
mediapipe/calculators/util/landmarks_to_detection_calculator_pb2.py,sha256=NTz0wFvqj-pJx8xTr42WA0XopVk5B2iXs73cq2e7qgE,1873
mediapipe/calculators/util/landmarks_to_floats_calculator_pb2.py,sha256=6P9eQAIo-GJcUsFYwRtRERUsbLgMNSbQx3G7crqrIC8,1859
mediapipe/calculators/util/landmarks_to_render_data_calculator_pb2.py,sha256=9AC8MO0igZM9-PohxhROwx0McSnVNpDTVghlHPv11g4,2811
mediapipe/calculators/util/landmarks_transformation_calculator_pb2.py,sha256=bbnQJonPFede6_ijgmqXz5L2mMY4-dklokdOnhXGPuU,3035
mediapipe/calculators/util/latency_pb2.py,sha256=a5RpmKckZynayJHbq3YbJmtVhgTJmmxuSksv_cVMUCU,1466
mediapipe/calculators/util/local_file_contents_calculator_pb2.py,sha256=uYXvGJueuggR6IrZHJFnXtpeMz4vF45cZM1_hDi8pDY,1843
mediapipe/calculators/util/logic_calculator_pb2.py,sha256=A2JzH5bdhHbvVpkMw3wLgVqqYCB8uu6YHkWcAQmn6Xs,2318
mediapipe/calculators/util/non_max_suppression_calculator_pb2.py,sha256=iw1NPGLwWdn-gqptire6ZORtXX6l49NEHK5zpsLX62I,2998
mediapipe/calculators/util/packet_frequency_calculator_pb2.py,sha256=4ysMk95d5t74Rq0_tyZA94o2nojD7-vNBYjwJfiKQl0,1864
mediapipe/calculators/util/packet_frequency_pb2.py,sha256=Ch1kumnTtAHBBf-f-gTzoXgsLSPYjsVMeetyeYsXzT4,1184
mediapipe/calculators/util/packet_latency_calculator_pb2.py,sha256=b70jiIStqqezKDW7l7cKiLXsdOBcnnLwollcl1SAPow,1991
mediapipe/calculators/util/rect_to_render_data_calculator_pb2.py,sha256=oBWWGBeVVAxws5FBy2sNGN3Ckqyo_KNE0xpXEvGRFYE,2154
mediapipe/calculators/util/rect_to_render_scale_calculator_pb2.py,sha256=t3qhu0dy0F73uz-KHmdAr1Evdded8cnabakEGFfvcq0,1926
mediapipe/calculators/util/rect_transformation_calculator_pb2.py,sha256=qshB0z1w0HRrMq3-u1a3gBLBMzWtjeAD2NXNH9w_Fz4,2149
mediapipe/calculators/util/refine_landmarks_from_heatmap_calculator_pb2.py,sha256=WfTC5nY3mQu_c7z9TqoVeMIC_BLgDySdhr_aEovq9yA,2109
mediapipe/calculators/util/resource_provider_calculator_pb2.py,sha256=FfyFTmKLROBf8KJ8lmdvoMSyl3SwmGydGcWwqeE1dWQ,1636
mediapipe/calculators/util/set_joints_visibility_calculator_pb2.py,sha256=yaZeGxDpoGTaNEA1FnEsOhZ3ujQKwy1ZZtTYWsqpx2k,3358
mediapipe/calculators/util/thresholding_calculator_pb2.py,sha256=y-QfYpMuQ0Z0j0h1n0sZc5Utiwp1-Mftq0ykbYb2XfQ,1796
mediapipe/calculators/util/timed_box_list_id_to_label_calculator_pb2.py,sha256=VdWVatATfHLrrjD9WF9yYuWRPnWwSMeR6UIPVKAXe9M,1883
mediapipe/calculators/util/timed_box_list_to_render_data_calculator_pb2.py,sha256=p0i59pZSftoE2jq74XYdedxR2Nw-Wu-CeiFrFg04PM8,2082
mediapipe/calculators/util/top_k_scores_calculator_pb2.py,sha256=hlap47M51t3sqquz959g0KxbjFy0Q6mFXHyhR-c08fM,1871
mediapipe/calculators/util/visibility_copy_calculator_pb2.py,sha256=AnbK-TTLXX0qYeSAaBqjz7xBAhzVxrM2Ist_NHWho9c,1588
mediapipe/calculators/util/visibility_smoothing_calculator_pb2.py,sha256=LIzbWiU_HsA72XG8wOYj8QFDL1mxyTIjx-bzjzzRdyM,2188
mediapipe/calculators/video/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/calculators/video/__pycache__/__init__.cpython-311.pyc,,
mediapipe/calculators/video/__pycache__/box_detector_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/video/__pycache__/box_tracker_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/video/__pycache__/flow_packager_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/video/__pycache__/flow_to_image_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/video/__pycache__/motion_analysis_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/video/__pycache__/opencv_video_encoder_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/video/__pycache__/tracked_detection_manager_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/video/__pycache__/video_pre_stream_calculator_pb2.cpython-311.pyc,,
mediapipe/calculators/video/box_detector_calculator_pb2.py,sha256=XLEjVGczZ2fSvNWCTJTjawgx0KG7VlCJ8wJh7sSNEC4,2063
mediapipe/calculators/video/box_tracker_calculator_pb2.py,sha256=Ao3o1-d7Y8jQ9jX2szgKgUqWf1h6vPnfWlOPAxxoONw,2416
mediapipe/calculators/video/flow_packager_calculator_pb2.py,sha256=Yh2flotn1YaRemYB7Im2HwYRja9iTrdrE__zQ6r5e4k,2170
mediapipe/calculators/video/flow_to_image_calculator_pb2.py,sha256=Vf0OONcNqqT6-Sb8HwlYfQjkaMg28df9TZY-SCY6D9s,1862
mediapipe/calculators/video/motion_analysis_calculator_pb2.py,sha256=-VkR3GV6APB6kCCCJzwTRVq1k3Lf9Bw2tIo8JbCMhek,3943
mediapipe/calculators/video/opencv_video_encoder_calculator_pb2.py,sha256=wZLMdMi7bIjhPGR-rt5m0Fto6N4HbRrz0pNi1AQi3oM,1998
mediapipe/calculators/video/tool/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/calculators/video/tool/__pycache__/__init__.cpython-311.pyc,,
mediapipe/calculators/video/tool/__pycache__/flow_quantizer_model_pb2.cpython-311.pyc,,
mediapipe/calculators/video/tool/flow_quantizer_model_pb2.py,sha256=fc70DUy-cx8bWKL6bal2v-tID0yh2MLxZ4pTlWIMv7g,1217
mediapipe/calculators/video/tracked_detection_manager_calculator_pb2.py,sha256=AqNMEcacql3ZNAkJu2ojssu52omxSGe2pn7hJsWfNNE,2171
mediapipe/calculators/video/video_pre_stream_calculator_pb2.py,sha256=0aYBh0NIleT6JAVdxTL6a33Gpx5fkNTXyXofU75hr84,2444
mediapipe/examples/__init__.py,sha256=bglKd2k2C7QGT1i-vstURXPJX2Cvq9FO9opr6cVeBp0,571
mediapipe/examples/__pycache__/__init__.cpython-311.pyc,,
mediapipe/examples/desktop/__init__.py,sha256=bglKd2k2C7QGT1i-vstURXPJX2Cvq9FO9opr6cVeBp0,571
mediapipe/examples/desktop/__pycache__/__init__.cpython-311.pyc,,
mediapipe/framework/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/framework/__pycache__/__init__.cpython-311.pyc,,
mediapipe/framework/__pycache__/calculator_options_pb2.cpython-311.pyc,,
mediapipe/framework/__pycache__/calculator_pb2.cpython-311.pyc,,
mediapipe/framework/__pycache__/calculator_profile_pb2.cpython-311.pyc,,
mediapipe/framework/__pycache__/graph_runtime_info_pb2.cpython-311.pyc,,
mediapipe/framework/__pycache__/mediapipe_options_pb2.cpython-311.pyc,,
mediapipe/framework/__pycache__/packet_factory_pb2.cpython-311.pyc,,
mediapipe/framework/__pycache__/packet_generator_pb2.cpython-311.pyc,,
mediapipe/framework/__pycache__/status_handler_pb2.cpython-311.pyc,,
mediapipe/framework/__pycache__/stream_handler_pb2.cpython-311.pyc,,
mediapipe/framework/__pycache__/test_calculators_pb2.cpython-311.pyc,,
mediapipe/framework/__pycache__/thread_pool_executor_pb2.cpython-311.pyc,,
mediapipe/framework/calculator_options_pb2.py,sha256=HDu6IeHNtaNcJVvu2kUtmDW_jQKHbuNeIjVFSDbkBls,1576
mediapipe/framework/calculator_pb2.py,sha256=ayTiojh341uo13xFhqMgf4iXcymy0MgiS7vEr0AIaOY,8397
mediapipe/framework/calculator_profile_pb2.py,sha256=Kk5SbRaqHcywt5O74x-rSAk2PK6xYf3mmkK2sCnEbPA,5670
mediapipe/framework/deps/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/framework/deps/__pycache__/__init__.cpython-311.pyc,,
mediapipe/framework/deps/__pycache__/proto_descriptor_pb2.cpython-311.pyc,,
mediapipe/framework/deps/proto_descriptor_pb2.py,sha256=wqxv1i5i7MTLWzFbHM00IO2D-jib9DDBDFcjrTMjvjE,2061
mediapipe/framework/formats/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/framework/formats/__pycache__/__init__.cpython-311.pyc,,
mediapipe/framework/formats/__pycache__/affine_transform_data_pb2.cpython-311.pyc,,
mediapipe/framework/formats/__pycache__/body_rig_pb2.cpython-311.pyc,,
mediapipe/framework/formats/__pycache__/classification_pb2.cpython-311.pyc,,
mediapipe/framework/formats/__pycache__/detection_pb2.cpython-311.pyc,,
mediapipe/framework/formats/__pycache__/image_file_properties_pb2.cpython-311.pyc,,
mediapipe/framework/formats/__pycache__/image_format_pb2.cpython-311.pyc,,
mediapipe/framework/formats/__pycache__/landmark_pb2.cpython-311.pyc,,
mediapipe/framework/formats/__pycache__/location_data_pb2.cpython-311.pyc,,
mediapipe/framework/formats/__pycache__/matrix_data_pb2.cpython-311.pyc,,
mediapipe/framework/formats/__pycache__/rect_pb2.cpython-311.pyc,,
mediapipe/framework/formats/__pycache__/time_series_header_pb2.cpython-311.pyc,,
mediapipe/framework/formats/affine_transform_data_pb2.py,sha256=R8j-hWpcm-4YkRCVsYfxE37GzDV5RjLUt0HAO9i2rpI,1562
mediapipe/framework/formats/annotation/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/framework/formats/annotation/__pycache__/__init__.cpython-311.pyc,,
mediapipe/framework/formats/annotation/__pycache__/locus_pb2.cpython-311.pyc,,
mediapipe/framework/formats/annotation/__pycache__/rasterization_pb2.cpython-311.pyc,,
mediapipe/framework/formats/annotation/locus_pb2.py,sha256=1rK8_LuTFgnDDiyjKbgE1IQf6Fu8zmCY-ASahjEpMRE,2368
mediapipe/framework/formats/annotation/rasterization_pb2.py,sha256=RCF8VMPZ6vJZvK91J75Ls79m0Zd87ewxMspsw2WiY9Y,1682
mediapipe/framework/formats/body_rig_pb2.py,sha256=xD8pORGimc2OwOSI-NSHR06NkS7pQJb5IzQ3xc9YgdM,1301
mediapipe/framework/formats/classification_pb2.py,sha256=tFZzex2Ak9WS0jhcU_8VCizTtM8OWIe-RaQ3K609LfA,1917
mediapipe/framework/formats/detection_pb2.py,sha256=5cTDu180su79SxWj5Pu4i1wple7Us2wi0bhaFjt2IUQ,2691
mediapipe/framework/formats/image_file_properties_pb2.py,sha256=0dLgyegQR3O2o4aWp7-Ol2yirQ-Dh-PiwoAGFetNinI,1376
mediapipe/framework/formats/image_format_pb2.py,sha256=gvhZqjRafs8jqrJPP9ptwBXA_1MQydi2YpSUQYLeB_Q,1787
mediapipe/framework/formats/landmark_pb2.py,sha256=jj1R7168bQzCdjSR2n4scnh7w-u75CEnCLxfVf-sUhA,2598
mediapipe/framework/formats/location_data_pb2.py,sha256=WbQsK59OwBEHWmMekISgZC2vFFxbsPNayeEcM4S0gH0,3377
mediapipe/framework/formats/matrix_data_pb2.py,sha256=FCKXhDteQlo8c_ahvs1o4GboK5_Na6C9PAbYORWsKkU,1839
mediapipe/framework/formats/motion/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/framework/formats/motion/__pycache__/__init__.cpython-311.pyc,,
mediapipe/framework/formats/motion/__pycache__/optical_flow_field_data_pb2.cpython-311.pyc,,
mediapipe/framework/formats/motion/optical_flow_field_data_pb2.py,sha256=vLAHFh2UPIHTpaTBw7Q8fEq3GUBEiAJVt8GbZkySeus,1667
mediapipe/framework/formats/object_detection/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/framework/formats/object_detection/__pycache__/__init__.cpython-311.pyc,,
mediapipe/framework/formats/object_detection/__pycache__/anchor_pb2.cpython-311.pyc,,
mediapipe/framework/formats/object_detection/anchor_pb2.py,sha256=PwoVR8N_qjp_yhHYqGHkBWt-eCyg-Tpm9NHtxP3sRwo,1242
mediapipe/framework/formats/rect_pb2.py,sha256=jGYZCf_V4P0Y8LNPKJ9rhx9gtTR5tN_g3g9HQJPX3zY,1809
mediapipe/framework/formats/time_series_header_pb2.py,sha256=P7bSFcb9T7bCgwfJ2kL6zRvDgqN5scXShyQys-S2Yaw,1679
mediapipe/framework/graph_runtime_info_pb2.py,sha256=9Zjedu4BQ2CfdjjIuGj-Owvx1mO4qWJhr20g1OGIO8A,2217
mediapipe/framework/mediapipe_options_pb2.py,sha256=Ua2GMb2rUC2T-XPq_9vuuwiM8JQ6alBcwZGMya2U9yI,1320
mediapipe/framework/packet_factory_pb2.py,sha256=1vDZX77iSVUWBsrzX2QfHvNjqQbH3EOLluViDVcp54w,1890
mediapipe/framework/packet_generator_pb2.py,sha256=u6Vo0qIg9p41k0Ctn3u6fTBwyGQpOquunHAw9dDa8zc,2134
mediapipe/framework/status_handler_pb2.py,sha256=H2TdAw1R_31SDH3t-xZnRsEFy2LNcFWJP8PC4DmxREo,1641
mediapipe/framework/stream_handler/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/framework/stream_handler/__pycache__/__init__.cpython-311.pyc,,
mediapipe/framework/stream_handler/__pycache__/default_input_stream_handler_pb2.cpython-311.pyc,,
mediapipe/framework/stream_handler/__pycache__/fixed_size_input_stream_handler_pb2.cpython-311.pyc,,
mediapipe/framework/stream_handler/__pycache__/sync_set_input_stream_handler_pb2.cpython-311.pyc,,
mediapipe/framework/stream_handler/__pycache__/timestamp_align_input_stream_handler_pb2.cpython-311.pyc,,
mediapipe/framework/stream_handler/default_input_stream_handler_pb2.py,sha256=pd5NZdNRKIBmDqVowjp1aEkcJfIoQZYyA_TBHk-p9Jc,1549
mediapipe/framework/stream_handler/fixed_size_input_stream_handler_pb2.py,sha256=lb1OxHFHT9xDqvjOvIJGCVGtdNb0kZqjvDwIR24mZBQ,1697
mediapipe/framework/stream_handler/sync_set_input_stream_handler_pb2.py,sha256=xFxIG7kifCWfPRxJ-E-RaLmliF9vWTHq7BS-x0Zgbds,1811
mediapipe/framework/stream_handler/timestamp_align_input_stream_handler_pb2.py,sha256=AuzQ8dH-8ew7XhZSHu01gERe2kqPfdl3mMur457Fpms,1604
mediapipe/framework/stream_handler_pb2.py,sha256=YGG3cOTcxZOh_P5iul7hexcEGJTJKZuJ3qRk5NOSd3s,1971
mediapipe/framework/test_calculators_pb2.py,sha256=qVQ0cVotiQ2jXWfshc04GEEN7SzFFCjRd4FIWwXBUs0,1932
mediapipe/framework/thread_pool_executor_pb2.py,sha256=SPOpq9tb6Ug8QPSq66OR0D_4qlBM3qZmVvOnaILvxCw,1983
mediapipe/framework/tool/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/framework/tool/__pycache__/__init__.cpython-311.pyc,,
mediapipe/framework/tool/__pycache__/calculator_graph_template_pb2.cpython-311.pyc,,
mediapipe/framework/tool/__pycache__/field_data_pb2.cpython-311.pyc,,
mediapipe/framework/tool/__pycache__/node_chain_subgraph_pb2.cpython-311.pyc,,
mediapipe/framework/tool/__pycache__/packet_generator_wrapper_calculator_pb2.cpython-311.pyc,,
mediapipe/framework/tool/__pycache__/source_pb2.cpython-311.pyc,,
mediapipe/framework/tool/__pycache__/switch_container_pb2.cpython-311.pyc,,
mediapipe/framework/tool/calculator_graph_template_pb2.py,sha256=6iice8v4wDgr7NcimoUCZvfgJHkBMynKBvchs4X0VvM,3954
mediapipe/framework/tool/field_data_pb2.py,sha256=KCR8b3JktZ1FfgcdJbzm-Lt4rjzU5zAAX6Koy_0vZXU,1774
mediapipe/framework/tool/node_chain_subgraph_pb2.py,sha256=n2s3sl7CORCfR2GJCJNKxkAPe_GsH64EsVPUU0MPJbY,1803
mediapipe/framework/tool/packet_generator_wrapper_calculator_pb2.py,sha256=dUpCagARizrzdd3eoizS5tqIrLBGUrOoBt6DMeNmjSY,1845
mediapipe/framework/tool/source_pb2.py,sha256=ZbQoLg9IrssTkV5XDfWtCvDP7vaDOQ_hx4X9VI0hIYQ,2303
mediapipe/framework/tool/switch_container_pb2.py,sha256=a8rGuQVDz2s-pMgEetFwCp3rBVjw6WpQgTW2ic2-9Ng,2225
mediapipe/gpu/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/gpu/__pycache__/__init__.cpython-311.pyc,,
mediapipe/gpu/__pycache__/copy_calculator_pb2.cpython-311.pyc,,
mediapipe/gpu/__pycache__/gl_animation_overlay_calculator_pb2.cpython-311.pyc,,
mediapipe/gpu/__pycache__/gl_context_options_pb2.cpython-311.pyc,,
mediapipe/gpu/__pycache__/gl_scaler_calculator_pb2.cpython-311.pyc,,
mediapipe/gpu/__pycache__/gl_surface_sink_calculator_pb2.cpython-311.pyc,,
mediapipe/gpu/__pycache__/gpu_origin_pb2.cpython-311.pyc,,
mediapipe/gpu/__pycache__/scale_mode_pb2.cpython-311.pyc,,
mediapipe/gpu/copy_calculator_pb2.py,sha256=BUpnE5jJVWHe9jrHEWm73Zt-qI2229FxGigN9vPmxGU,2003
mediapipe/gpu/gl_animation_overlay_calculator_pb2.py,sha256=hFiG7vCSrEdRbvO1DqHdLJMah3LxbMAMzymkT6lqr0g,2085
mediapipe/gpu/gl_context_options_pb2.py,sha256=PNr0naBig9cBmMFtfrvcRlXEZAQD8URQ6Ki33uKN4gY,1688
mediapipe/gpu/gl_scaler_calculator_pb2.py,sha256=uLNQVZ-ks0kOPfbzZ2ujxItvTEkauPKTyE99E1RR1dM,2304
mediapipe/gpu/gl_surface_sink_calculator_pb2.py,sha256=Zis415nD76srdUS31vAnLq98MftW8p67EDCbqc3aFeE,1933
mediapipe/gpu/gpu_origin_pb2.py,sha256=rJJSJg89Nj0ucZm0Gu9wyWF1pT_FTHPJ56NisPjNutw,1428
mediapipe/gpu/scale_mode_pb2.py,sha256=kNQTHkafsIcFSx6XpKjkTlt9sIdsybZC6xVrlhGSzgc,1271
mediapipe/model_maker/__init__.py,sha256=IswWcmeOPaRBNNdVp3K6m_ISXrI9_Enxnp-1kiaUEKs,1153
mediapipe/model_maker/__pycache__/__init__.cpython-311.pyc,,
mediapipe/model_maker/__pycache__/setup.cpython-311.pyc,,
mediapipe/model_maker/setup.py,sha256=GaOQVu7uTMp3gjpDHIVV45GEnLvSrgvg-BJoJAJXMbI,4064
mediapipe/modules/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/modules/__pycache__/__init__.cpython-311.pyc,,
mediapipe/modules/face_detection/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/modules/face_detection/__pycache__/__init__.cpython-311.pyc,,
mediapipe/modules/face_detection/__pycache__/face_detection_pb2.cpython-311.pyc,,
mediapipe/modules/face_detection/face_detection_full_range_cpu.binarypb,sha256=APBy5xqXUlNqpLCSFsa_jZzHSTIiqNOkcuVj6w4jFjs,260
mediapipe/modules/face_detection/face_detection_full_range_sparse.tflite,sha256=LDco5tpW8h4hoyBDM5b7BtQNkIjyJHwF5WNaaI1F3-E,676746
mediapipe/modules/face_detection/face_detection_pb2.py,sha256=HgH3bFy1fc6K89qp9rMKFBIUBcxJ7Y-cDjYhxfsT3xE,2688
mediapipe/modules/face_detection/face_detection_short_range.tflite,sha256=u_8Rzr0esnoeAEyuCw5j7IxVHL80pEURSLSQi42z7Kg,229714
mediapipe/modules/face_detection/face_detection_short_range_cpu.binarypb,sha256=hOZXlNTh0cbVaJb036q5e8dpAzIFR1wbuZZ6IvDaRpw,262
mediapipe/modules/face_geometry/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/modules/face_geometry/__pycache__/__init__.cpython-311.pyc,,
mediapipe/modules/face_geometry/__pycache__/effect_renderer_calculator_pb2.cpython-311.pyc,,
mediapipe/modules/face_geometry/__pycache__/env_generator_calculator_pb2.cpython-311.pyc,,
mediapipe/modules/face_geometry/__pycache__/geometry_pipeline_calculator_pb2.cpython-311.pyc,,
mediapipe/modules/face_geometry/data/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/modules/face_geometry/data/__pycache__/__init__.cpython-311.pyc,,
mediapipe/modules/face_geometry/effect_renderer_calculator_pb2.py,sha256=CKAs1GPxCUmfU9WHs3r9kANGdnFjTEUS8zZ6SZS67fI,1668
mediapipe/modules/face_geometry/env_generator_calculator_pb2.py,sha256=kJ4Ai4F5LbHDHjpHjrZGxa_blfaIffnP6b4u9Pqsuq0,1820
mediapipe/modules/face_geometry/geometry_pipeline_calculator_pb2.py,sha256=8ssTCIrQnCJL7BfhJZfpvRgZVp1VyK0xWICBrfjSts4,1563
mediapipe/modules/face_geometry/libs/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/modules/face_geometry/libs/__pycache__/__init__.cpython-311.pyc,,
mediapipe/modules/face_geometry/protos/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/modules/face_geometry/protos/__pycache__/__init__.cpython-311.pyc,,
mediapipe/modules/face_geometry/protos/__pycache__/environment_pb2.cpython-311.pyc,,
mediapipe/modules/face_geometry/protos/__pycache__/face_geometry_pb2.cpython-311.pyc,,
mediapipe/modules/face_geometry/protos/__pycache__/geometry_pipeline_metadata_pb2.cpython-311.pyc,,
mediapipe/modules/face_geometry/protos/__pycache__/mesh_3d_pb2.cpython-311.pyc,,
mediapipe/modules/face_geometry/protos/environment_pb2.py,sha256=RTqtEaMRzjBaWobtq4r1DlkRgOI5oNrEtofIqGUkTfg,2014
mediapipe/modules/face_geometry/protos/face_geometry_pb2.py,sha256=6Ni8D2tT9XfFv0QBVpuPMwy7LqtcHksaeYMlcU9bXHM,1873
mediapipe/modules/face_geometry/protos/geometry_pipeline_metadata_pb2.py,sha256=8mdhJo-ZTEZa_f87cTjQ79bEKfYR2fUNR7--2eUaSjA,2401
mediapipe/modules/face_geometry/protos/mesh_3d_pb2.py,sha256=XB5cEL1wq9kkXvJrn6iUyIbIf7GIv0Bah0ci6HhQqLo,1886
mediapipe/modules/face_landmark/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/modules/face_landmark/__pycache__/__init__.cpython-311.pyc,,
mediapipe/modules/face_landmark/face_landmark.tflite,sha256=EFXLnUqcqLjGiJAqOlGUMRE4uiVrzJTjNtg3Ol8wyBQ,1242398
mediapipe/modules/face_landmark/face_landmark_front_cpu.binarypb,sha256=-jwJLaJ9Ax1U3dYaKbDj3VIdU_VfqImMsPK2TXnNZXM,2361
mediapipe/modules/face_landmark/face_landmark_with_attention.tflite,sha256=4GqATgFE-ZKe2nghIpFrNdYMaXw8k0QBPKK752ps4rQ,2495106
mediapipe/modules/hand_landmark/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/modules/hand_landmark/__pycache__/__init__.cpython-311.pyc,,
mediapipe/modules/hand_landmark/calculators/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/modules/hand_landmark/calculators/__pycache__/__init__.cpython-311.pyc,,
mediapipe/modules/hand_landmark/hand_landmark_full.tflite,sha256=EcJyuJHhqZqwNCCOI5N6gAg4jPEe0qnXdu09AdC6AOM,5478917
mediapipe/modules/hand_landmark/hand_landmark_lite.tflite,sha256=BI7dNkXJv3OX0ZqfbjpClX1uQUyb6mWYAwoum2JBVuY,2071597
mediapipe/modules/hand_landmark/hand_landmark_tracking_cpu.binarypb,sha256=lCrU5rPs0ZA_0nQ60iVaNBhU3pde-ckEGKTHtk8LRCU,2901
mediapipe/modules/hand_landmark/handedness.txt,sha256=O4LwJGhw_sRWyGMtRVpOHVZI0oNqelE6JxeBFE9SWRY,11
mediapipe/modules/holistic_landmark/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/modules/holistic_landmark/__pycache__/__init__.cpython-311.pyc,,
mediapipe/modules/holistic_landmark/calculators/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/modules/holistic_landmark/calculators/__pycache__/__init__.cpython-311.pyc,,
mediapipe/modules/holistic_landmark/calculators/__pycache__/roi_tracking_calculator_pb2.cpython-311.pyc,,
mediapipe/modules/holistic_landmark/calculators/roi_tracking_calculator_pb2.py,sha256=nKGhtRHAvQ7ZU6TDo-Fn08NSC6SZ95p6f8viGWBmazo,3020
mediapipe/modules/holistic_landmark/hand_recrop.tflite,sha256=Z9mWzpb502_hfSaTAixtqTFoAmqy8Cj54jZTmNisfV0,123792
mediapipe/modules/holistic_landmark/holistic_landmark_cpu.binarypb,sha256=wdo-qSzQ366zWhs4Y6OjQgLG9L7_5BDv9KM3VpyAj70,1370
mediapipe/modules/iris_landmark/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/modules/iris_landmark/__pycache__/__init__.cpython-311.pyc,,
mediapipe/modules/iris_landmark/iris_landmark.tflite,sha256=0XRNKgnCX1AdOeuk-v9H5T7MqIUsXOGbzo7qw5NXUh8,2640568
mediapipe/modules/objectron/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/modules/objectron/__pycache__/__init__.cpython-311.pyc,,
mediapipe/modules/objectron/calculators/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/modules/objectron/calculators/__pycache__/__init__.cpython-311.pyc,,
mediapipe/modules/objectron/calculators/__pycache__/a_r_capture_metadata_pb2.cpython-311.pyc,,
mediapipe/modules/objectron/calculators/__pycache__/annotation_data_pb2.cpython-311.pyc,,
mediapipe/modules/objectron/calculators/__pycache__/belief_decoder_config_pb2.cpython-311.pyc,,
mediapipe/modules/objectron/calculators/__pycache__/camera_parameters_pb2.cpython-311.pyc,,
mediapipe/modules/objectron/calculators/__pycache__/filter_detection_calculator_pb2.cpython-311.pyc,,
mediapipe/modules/objectron/calculators/__pycache__/frame_annotation_to_rect_calculator_pb2.cpython-311.pyc,,
mediapipe/modules/objectron/calculators/__pycache__/frame_annotation_tracker_calculator_pb2.cpython-311.pyc,,
mediapipe/modules/objectron/calculators/__pycache__/lift_2d_frame_annotation_to_3d_calculator_pb2.cpython-311.pyc,,
mediapipe/modules/objectron/calculators/__pycache__/object_pb2.cpython-311.pyc,,
mediapipe/modules/objectron/calculators/__pycache__/tensors_to_objects_calculator_pb2.cpython-311.pyc,,
mediapipe/modules/objectron/calculators/__pycache__/tflite_tensors_to_objects_calculator_pb2.cpython-311.pyc,,
mediapipe/modules/objectron/calculators/a_r_capture_metadata_pb2.py,sha256=ms4Gp4d-guqPMReElXJLmnX_zv5yGWTBKDEvbyMJ-v8,13779
mediapipe/modules/objectron/calculators/annotation_data_pb2.py,sha256=m67-pY_bYJeN_vphOa54r19jS1NHCw5-9dEAMyJs_60,3356
mediapipe/modules/objectron/calculators/belief_decoder_config_pb2.py,sha256=xeA80Gcsm4a32-nTN03A-MiayxUtLxRr09QrWYwqoE4,1704
mediapipe/modules/objectron/calculators/camera_parameters_pb2.py,sha256=vTQcmvHFih42hP_GfLHK3X6b_Mu_ysLl-6BkS8AkECY,2195
mediapipe/modules/objectron/calculators/filter_detection_calculator_pb2.py,sha256=r075bW-EQUMqsk9pyMwBVT8AsQWBy8yEPj00GhMUx2I,2643
mediapipe/modules/objectron/calculators/frame_annotation_to_rect_calculator_pb2.py,sha256=-ZnzrvrELbtPkdZcFfRkRDAdW2ecgiXj8R3uZ84LyrM,1988
mediapipe/modules/objectron/calculators/frame_annotation_tracker_calculator_pb2.py,sha256=wAY46_5M_yA_-aESV4LHTTLRFbGlbUsQkrwN-EMKHhU,2016
mediapipe/modules/objectron/calculators/lift_2d_frame_annotation_to_3d_calculator_pb2.py,sha256=3yvZ5C2ZN0XY2PWgDF-aAs_5bVnaGWegBi4_VvBCTIE,2498
mediapipe/modules/objectron/calculators/object_pb2.py,sha256=ZCG94OWowqeP9721h-RGDF0O5rVf4RSW5J00ESG7Cbc,2923
mediapipe/modules/objectron/calculators/tensors_to_objects_calculator_pb2.py,sha256=yTyIQKW_Vv9966tVtFZ-VPWViGhF1sJeWNO_XgFfAUM,2313
mediapipe/modules/objectron/calculators/tflite_tensors_to_objects_calculator_pb2.py,sha256=WbxV-S2mOw2AIqV1a6MURlTXpZddk_30dXdpPPC4hkc,2610
mediapipe/modules/objectron/object_detection_oidv4_labelmap.txt,sha256=wUgzamX2cwE2Cakj_cve7A9J35NQRQKcN0ajgooOgOA,184
mediapipe/modules/objectron/objectron_cpu.binarypb,sha256=lu3H83WDBFhYeEXV4w3omPOcIfUo7FxYuv2hvWWyoYo,2267
mediapipe/modules/palm_detection/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/modules/palm_detection/__pycache__/__init__.cpython-311.pyc,,
mediapipe/modules/palm_detection/palm_detection_full.tflite,sha256=GxTpQixq0AbN5lgaRsi5DdVzwHq385NLVYnnzqP4mlQ,2339846
mediapipe/modules/palm_detection/palm_detection_lite.tflite,sha256=6aSq3fkN2laocjUwPPAOTC0_socl9o_Yh3KZfayQXBg,1985440
mediapipe/modules/pose_detection/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/modules/pose_detection/__pycache__/__init__.cpython-311.pyc,,
mediapipe/modules/pose_detection/pose_detection.tflite,sha256=m6ndPULvqrqGtP8BIrBvKcQSLnVrMp2J3KHil_2Phmw,2959046
mediapipe/modules/pose_landmark/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/modules/pose_landmark/__pycache__/__init__.cpython-311.pyc,,
mediapipe/modules/pose_landmark/pose_landmark_cpu.binarypb,sha256=kN3HF2JaPBrt_coSQhKjbOWVzbvkZRP9k63KUgLY_yE,2418
mediapipe/modules/pose_landmark/pose_landmark_full.tflite,sha256=6aXFyxf3Nvr9TC7B2js9Mx1u2-ig0yOVhVrrLN_WS58,6440512
mediapipe/modules/selfie_segmentation/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/modules/selfie_segmentation/__pycache__/__init__.cpython-311.pyc,,
mediapipe/modules/selfie_segmentation/selfie_segmentation.tflite,sha256=nuFo7HyPKhbFb-jhz7xRSXTLu35DQFG0VWNfG9FGL1w,249505
mediapipe/modules/selfie_segmentation/selfie_segmentation_cpu.binarypb,sha256=358pHq3CrXekoPq9g9TywMK02GJsj0T7DRTvwvyB9G0,815
mediapipe/modules/selfie_segmentation/selfie_segmentation_landscape.tflite,sha256=p30D9GWbn2tsH1EGlHv0DpnXZVCUtlJ_IU6n1FEQbt0,250145
mediapipe/python/__init__.py,sha256=BQglgytZUe7_ZuD8amosz-szWdJ2LQp81nsuiEY3W84,1493
mediapipe/python/__pycache__/__init__.cpython-311.pyc,,
mediapipe/python/__pycache__/calculator_graph_test.cpython-311.pyc,,
mediapipe/python/__pycache__/image_frame_test.cpython-311.pyc,,
mediapipe/python/__pycache__/image_test.cpython-311.pyc,,
mediapipe/python/__pycache__/packet_creator.cpython-311.pyc,,
mediapipe/python/__pycache__/packet_getter.cpython-311.pyc,,
mediapipe/python/__pycache__/packet_test.cpython-311.pyc,,
mediapipe/python/__pycache__/solution_base.cpython-311.pyc,,
mediapipe/python/__pycache__/solution_base_test.cpython-311.pyc,,
mediapipe/python/__pycache__/timestamp_test.cpython-311.pyc,,
mediapipe/python/_framework_bindings.cpython-311-darwin.so,sha256=_wJZCrgNDiYd5eXjRDWm6B0T0pY9ugTZne3GbTkLcu0,75552904
mediapipe/python/_framework_bindings/arm64.cpython-311-darwin.so,sha256=Fg1BENk8L-ITRJueAuzHdqIzH9JoOnzI4OHvH1oWuC0,4152
mediapipe/python/calculator_graph_test.py,sha256=GpUQPxetP803jNNLyaI5pxujIvKsMutB5dGoEsOVR2M,8876
mediapipe/python/image_frame_test.py,sha256=ZSjdE-an2t8i6MiA4_Xri91VMH5_CCx45fjhWUQptMY,8602
mediapipe/python/image_test.py,sha256=3YABND327mtfbEMJyr9wr-_Ilhrf5tyxNaIGaiGDBIU,9436
mediapipe/python/packet_creator.py,sha256=yUrRQL5B-wFjfSLM34qBXYI13JGLZ-P8_bE31S7iuXU,11502
mediapipe/python/packet_getter.py,sha256=vD8empqGtMG2-OLFahqKorqNohU9S3iHBxZL2i3IcGs,4235
mediapipe/python/packet_test.py,sha256=SrAkhIptP8Yk0yL6PoHwfs9OT1iDzAwz79m-TsImlmk,22306
mediapipe/python/solution_base.py,sha256=nEIqsho9DlutfvWWzdSxCOpJ2QzN7n2938WLDmFzn38,26072
mediapipe/python/solution_base_test.py,sha256=1u5Lo4aEUrMKj8Ha_34XMyKnI-3A1AvpaX3MCI0b2MM,15632
mediapipe/python/solutions/__init__.py,sha256=wta23yyLyQthEl5lHe4gKxaKs1sqyp2RLlX6HOUc8iE,1145
mediapipe/python/solutions/__pycache__/__init__.cpython-311.pyc,,
mediapipe/python/solutions/__pycache__/download_utils.cpython-311.pyc,,
mediapipe/python/solutions/__pycache__/drawing_styles.cpython-311.pyc,,
mediapipe/python/solutions/__pycache__/drawing_utils.cpython-311.pyc,,
mediapipe/python/solutions/__pycache__/drawing_utils_test.cpython-311.pyc,,
mediapipe/python/solutions/__pycache__/face_detection.cpython-311.pyc,,
mediapipe/python/solutions/__pycache__/face_detection_test.cpython-311.pyc,,
mediapipe/python/solutions/__pycache__/face_mesh.cpython-311.pyc,,
mediapipe/python/solutions/__pycache__/face_mesh_connections.cpython-311.pyc,,
mediapipe/python/solutions/__pycache__/face_mesh_test.cpython-311.pyc,,
mediapipe/python/solutions/__pycache__/hands.cpython-311.pyc,,
mediapipe/python/solutions/__pycache__/hands_connections.cpython-311.pyc,,
mediapipe/python/solutions/__pycache__/hands_test.cpython-311.pyc,,
mediapipe/python/solutions/__pycache__/holistic.cpython-311.pyc,,
mediapipe/python/solutions/__pycache__/holistic_test.cpython-311.pyc,,
mediapipe/python/solutions/__pycache__/objectron.cpython-311.pyc,,
mediapipe/python/solutions/__pycache__/objectron_test.cpython-311.pyc,,
mediapipe/python/solutions/__pycache__/pose.cpython-311.pyc,,
mediapipe/python/solutions/__pycache__/pose_connections.cpython-311.pyc,,
mediapipe/python/solutions/__pycache__/pose_test.cpython-311.pyc,,
mediapipe/python/solutions/__pycache__/selfie_segmentation.cpython-311.pyc,,
mediapipe/python/solutions/__pycache__/selfie_segmentation_test.cpython-311.pyc,,
mediapipe/python/solutions/download_utils.py,sha256=DQr0b340aUgfloKCmZMjH-knsBRbIJW4lTgX4CzTPO8,1508
mediapipe/python/solutions/drawing_styles.py,sha256=u52h0xbgNPzzqAKhX7Yt2Nrxdloq_br_8nivNp2CoAM,9599
mediapipe/python/solutions/drawing_utils.py,sha256=di2hvd8HmBcMErDdRKto7WgKr6ralq5w-rYAy76hx-U,13775
mediapipe/python/solutions/drawing_utils_test.py,sha256=5YGiD-hdtSbtW4mvPlnIYbhlSAQ4wIHwj22qCzHFzW0,11848
mediapipe/python/solutions/face_detection.py,sha256=KNLDRKssdSUkS3oGIgfYvDH_QBUu7z9Ja9FuHJi0VRI,3772
mediapipe/python/solutions/face_detection_test.py,sha256=qgHrbywIuw39sF9UB5DYtkHWJIEPojeVdfsxR8YRyxI,3776
mediapipe/python/solutions/face_mesh.py,sha256=gUc_8snx8ENZ2L2W6qcMbXg503BvKGdDQ5MmaFVpYYc,5886
mediapipe/python/solutions/face_mesh_connections.py,sha256=zHFx5E0GEts4cgyy_SUWw5WZLN10V-_jQVQ75vPCLYc,36170
mediapipe/python/solutions/face_mesh_test.py,sha256=cmpXx4JZr7SinUjQixgmGPhge5IEhfTSLeSxsWdyots,5919
mediapipe/python/solutions/hands.py,sha256=TuoT4PY-rlzy3CFEjqeN849MkKKjb0Px242Hl_eItRo,6132
mediapipe/python/solutions/hands_connections.py,sha256=KHShpe4oSSykZGJNRlYSgPNYbuyaK-gJ2AAkBVH2PT0,1226
mediapipe/python/solutions/hands_test.py,sha256=DPj-mvn_tFLU-0oJpryvBeWuLKUT-nQaXEb7yq5hGLE,9481
mediapipe/python/solutions/holistic.py,sha256=qcXP-MS2G7NUa9AB1HU2ZsveT2hvHdxTIN6zeOpu-eo,8226
mediapipe/python/solutions/holistic_test.py,sha256=81K7WvWBNiDjAT__DDFgC7V2i5U3spng29oBRKi28-U,6746
mediapipe/python/solutions/objectron.py,sha256=GU9oWOp-RHXchvS833n7_ifYYqEFMDBUlx1iIE9cHcQ,11653
mediapipe/python/solutions/objectron_test.py,sha256=dBYaGQtaY1DbEKHguBgg_SoP7tEUjng2IRwwyU_kbb0,3304
mediapipe/python/solutions/pose.py,sha256=uer1phbEF2aI00KQZRp3GqWK03L68PVaU1Lcmy9LFV4,7921
mediapipe/python/solutions/pose_connections.py,sha256=bY6IbTOXpb9_SmcD05sn2qo8-A3Ni8zgsRH2i9W9xuw,1166
mediapipe/python/solutions/pose_test.py,sha256=TKhiHbrQroAoh7N8u1ttIapp0uiGn8lP60-NGnwpGRU,11418
mediapipe/python/solutions/selfie_segmentation.py,sha256=YAoacNZn_JD3s5VYBjAbOopR4XQhOmWjIImZfH0pKyY,2774
mediapipe/python/solutions/selfie_segmentation_test.py,sha256=x1nPszVUxtOwibYd-LmbrkGFhjO3Fwb6h4AF9REbMgE,2690
mediapipe/python/timestamp_test.py,sha256=oWKTZMsV586jH57OBV30rihcymETyGC29VbYURNLJQQ,2528
mediapipe/tasks/__init__.py,sha256=sVJS2p8J2PNVl8DLRPVY6KLpHenP_z3QVPRU0x_iL5g,571
mediapipe/tasks/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/audio/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/audio/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/audio/audio_classifier/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/audio/audio_classifier/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/audio/audio_classifier/proto/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/audio/audio_classifier/proto/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/audio/audio_classifier/proto/__pycache__/audio_classifier_graph_options_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/audio/audio_classifier/proto/audio_classifier_graph_options_pb2.py,sha256=MwfGjbfsYob-_IWeR3HPuK8I_0QB4oGdN_x_lkNwE_s,3045
mediapipe/tasks/cc/audio/audio_embedder/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/audio/audio_embedder/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/audio/audio_embedder/proto/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/audio/audio_embedder/proto/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/audio/audio_embedder/proto/__pycache__/audio_embedder_graph_options_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/audio/audio_embedder/proto/audio_embedder_graph_options_pb2.py,sha256=tB_MhudHZs_RafWhPtcKrg4ac1Omm7BWoyZGOGiDG9I,2942
mediapipe/tasks/cc/audio/core/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/audio/core/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/audio/utils/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/audio/utils/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/components/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/components/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/components/calculators/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/components/calculators/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/components/calculators/__pycache__/classification_aggregation_calculator_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/components/calculators/__pycache__/score_calibration_calculator_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/components/calculators/__pycache__/tensors_to_embeddings_calculator_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/components/calculators/classification_aggregation_calculator_pb2.py,sha256=eJ8BU5rQZLH9ZrT7MbKW0rqvCavypaCogTKBfbKW87g,1938
mediapipe/tasks/cc/components/calculators/score_calibration_calculator_pb2.py,sha256=e3MoTzhnQOQxW6hEEzjwaPB8ksUpwaoXfM11WaUonCw,2795
mediapipe/tasks/cc/components/calculators/tensors_to_embeddings_calculator_pb2.py,sha256=4G0R5npWy7CAQf2DUN6yrvmx8zgFxQQp2J33gjXLT6I,2311
mediapipe/tasks/cc/components/containers/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/components/containers/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/components/containers/proto/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/components/containers/proto/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/components/containers/proto/__pycache__/classifications_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/components/containers/proto/__pycache__/embeddings_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/components/containers/proto/__pycache__/landmarks_detection_result_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/components/containers/proto/classifications_pb2.py,sha256=zT1fFIaH6oYvI9-onIbE0_OkPRD3bB3VORaZFbA_IdU,2102
mediapipe/tasks/cc/components/containers/proto/embeddings_pb2.py,sha256=J64JK15nafXlr5YeOxykjzgp9CxmmFOsVmrgBNY8348,2511
mediapipe/tasks/cc/components/containers/proto/landmarks_detection_result_pb2.py,sha256=NiQEVQ4_WSphjXN-yT1n1RVGaoRs3mDpZiSMjaHtS7A,2769
mediapipe/tasks/cc/components/processors/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/components/processors/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/components/processors/proto/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/components/processors/proto/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/components/processors/proto/__pycache__/classification_postprocessing_graph_options_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/components/processors/proto/__pycache__/classifier_options_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/components/processors/proto/__pycache__/detection_postprocessing_graph_options_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/components/processors/proto/__pycache__/detector_options_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/components/processors/proto/__pycache__/embedder_options_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/components/processors/proto/__pycache__/embedding_postprocessing_graph_options_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/components/processors/proto/__pycache__/image_preprocessing_graph_options_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/components/processors/proto/__pycache__/text_model_type_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/components/processors/proto/__pycache__/text_preprocessing_graph_options_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/components/processors/proto/classification_postprocessing_graph_options_pb2.py,sha256=kDsyyomUMf4Q5BL2LVr3a4tH--UQyNBq6SWBLlTLhmE,3901
mediapipe/tasks/cc/components/processors/proto/classifier_options_pb2.py,sha256=7uFpgWBPv_gX2Ypw-kwyzMW1eT3L4fY7D4dK67x_JSA,1716
mediapipe/tasks/cc/components/processors/proto/detection_postprocessing_graph_options_pb2.py,sha256=niTbNsJ6toYpL8Rg-yKGKguOA1VXK-XZKsZds1NWQgQ,3899
mediapipe/tasks/cc/components/processors/proto/detector_options_pb2.py,sha256=Mozhgud6aXXc6VoCLf9giARrTwcvlGEaVRAB6OQDDSc,1822
mediapipe/tasks/cc/components/processors/proto/embedder_options_pb2.py,sha256=u_gH7PmvpKsoAvJ6UXpIiRbfpT-9Y4Y8OGBVX-FoaWE,1516
mediapipe/tasks/cc/components/processors/proto/embedding_postprocessing_graph_options_pb2.py,sha256=MEE27PZyc-voT07urhLb4GLy2PAg7dpqgmmz_Tu7eXM,2405
mediapipe/tasks/cc/components/processors/proto/image_preprocessing_graph_options_pb2.py,sha256=mniLSsuiyFgeAlZjDwUr5HKxoloMs5hsPf4q17IOWCY,2682
mediapipe/tasks/cc/components/processors/proto/text_model_type_pb2.py,sha256=qOBrok-l2XJD22KAhOKJTQ65ieKV5a2uNx4aIBMn-64,1482
mediapipe/tasks/cc/components/processors/proto/text_preprocessing_graph_options_pb2.py,sha256=s7krURiWQrsd2JEdg87vtjKT2M1ZIZKwWrMCJ2JXyq8,2380
mediapipe/tasks/cc/components/utils/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/components/utils/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/core/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/core/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/core/proto/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/core/proto/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/core/proto/__pycache__/acceleration_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/core/proto/__pycache__/base_options_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/core/proto/__pycache__/external_file_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/core/proto/__pycache__/inference_subgraph_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/core/proto/__pycache__/model_resources_calculator_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/core/proto/acceleration_pb2.py,sha256=V9rfSM455gu1IEwVs5F_i5EqGt37nIkzPKxwfaIiOfI,1958
mediapipe/tasks/cc/core/proto/base_options_pb2.py,sha256=zF3jbbeagO7mOFg-WE8xF9AfUZ2mTQvqdB-BpJnh0Dk,2140
mediapipe/tasks/cc/core/proto/external_file_pb2.py,sha256=Fi0StyB965sScPG-RL0HBdhk_Tbh2yy_V4vt5DSyRgo,2064
mediapipe/tasks/cc/core/proto/inference_subgraph_pb2.py,sha256=8APC6ejvjNeD4tWId8YlZX8fWKCfEZZgAuidkHlO5sM,2102
mediapipe/tasks/cc/core/proto/model_resources_calculator_pb2.py,sha256=MLCe3iuWsKVTWdevA08VirGBwfniLYA44S8zHvOtE8A,2144
mediapipe/tasks/cc/genai/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/genai/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/genai/inference/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/genai/inference/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/genai/inference/c/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/genai/inference/c/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/genai/inference/calculators/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/genai/inference/calculators/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/genai/inference/calculators/__pycache__/detokenizer_calculator_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/genai/inference/calculators/__pycache__/llm_gpu_calculator_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/genai/inference/calculators/__pycache__/model_data_calculator_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/genai/inference/calculators/__pycache__/tokenizer_calculator_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/genai/inference/calculators/detokenizer_calculator_pb2.py,sha256=MyK8c6glWE7ZkCWsyz1Y4L9HsmXoodC_Ma7Qo70rdf8,1686
mediapipe/tasks/cc/genai/inference/calculators/llm_gpu_calculator_pb2.py,sha256=lihPOGPVAuR9Rmgeo43d8mbPGHpi0R9mGyhvckZOuX0,3492
mediapipe/tasks/cc/genai/inference/calculators/model_data_calculator_pb2.py,sha256=_rwmwavK616chIS9FbxPlg1p7gn5e_PdwFpG5NEwk5w,1543
mediapipe/tasks/cc/genai/inference/calculators/tokenizer_calculator_pb2.py,sha256=kkYogvf_CKGgLj6gdVHyHvwBeA90lDq5ENSPj4xFZoo,1992
mediapipe/tasks/cc/genai/inference/common/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/genai/inference/common/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/genai/inference/proto/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/genai/inference/proto/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/genai/inference/proto/__pycache__/llm_file_metadata_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/genai/inference/proto/__pycache__/llm_params_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/genai/inference/proto/__pycache__/prompt_template_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/genai/inference/proto/__pycache__/sampler_params_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/genai/inference/proto/__pycache__/transformer_params_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/genai/inference/proto/llm_file_metadata_pb2.py,sha256=BfmfU6brGPp8JfPGdnUjbJN2C1zHm1EHUqyfebiDoig,2454
mediapipe/tasks/cc/genai/inference/proto/llm_params_pb2.py,sha256=FhPsB0JKGlP3gxI81dZmCaec6IoPt3O5tNWZLe3gOdg,4004
mediapipe/tasks/cc/genai/inference/proto/prompt_template_pb2.py,sha256=VZ2yTgzKzfmM7m6fo2cuxa1WV4vWACUhFmGgVxvjx5A,1464
mediapipe/tasks/cc/genai/inference/proto/sampler_params_pb2.py,sha256=Muc3Iz63rSTDrpxwrHl103dEui8UirlFzgf5t8f1X28,1860
mediapipe/tasks/cc/genai/inference/proto/transformer_params_pb2.py,sha256=liGybuvGfDNacQIS2B4wWWVHgYAKMY16Nbg4hLFQeq4,6650
mediapipe/tasks/cc/genai/inference/utils/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/genai/inference/utils/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/genai/inference/utils/llm_utils/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/genai/inference/utils/llm_utils/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/genai/inference/utils/xnn_utils/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/genai/inference/utils/xnn_utils/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/metadata/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/metadata/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/metadata/python/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/metadata/python/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/metadata/python/_pywrap_metadata_version.cpython-311-darwin.so,sha256=WXcMgnN54cBhz3-4LvsjL6MuYIsrQeYcQdru_sBJ4Kc,570968
mediapipe/tasks/cc/metadata/python/_pywrap_metadata_version/arm64.cpython-311-darwin.so,sha256=Fg1BENk8L-ITRJueAuzHdqIzH9JoOnzI4OHvH1oWuC0,4152
mediapipe/tasks/cc/metadata/tests/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/metadata/tests/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/metadata/utils/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/metadata/utils/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/text/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/text/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/text/custom_ops/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/text/custom_ops/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/text/custom_ops/ragged/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/text/custom_ops/ragged/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/text/custom_ops/sentencepiece/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/text/custom_ops/sentencepiece/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/text/custom_ops/sentencepiece/testdata/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/text/custom_ops/sentencepiece/testdata/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/text/language_detector/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/text/language_detector/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/text/language_detector/custom_ops/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/text/language_detector/custom_ops/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/text/language_detector/custom_ops/utils/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/text/language_detector/custom_ops/utils/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/text/language_detector/custom_ops/utils/hash/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/text/language_detector/custom_ops/utils/hash/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/text/language_detector/custom_ops/utils/utf/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/text/language_detector/custom_ops/utils/utf/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/text/text_classifier/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/text/text_classifier/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/text/text_classifier/proto/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/text/text_classifier/proto/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/text/text_classifier/proto/__pycache__/text_classifier_graph_options_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/text/text_classifier/proto/text_classifier_graph_options_pb2.py,sha256=CSJUsoXOJpaU-b_NAoEFYNDfABlkkb92UnYGTFy0Kxc,2952
mediapipe/tasks/cc/text/text_embedder/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/text/text_embedder/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/text/text_embedder/proto/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/text/text_embedder/proto/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/text/text_embedder/proto/__pycache__/text_embedder_graph_options_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/text/text_embedder/proto/text_embedder_graph_options_pb2.py,sha256=4UHa5h1LQDjKKEDSpoPhH4WT8N_Q9RtT_WDqTc40KPI,2913
mediapipe/tasks/cc/text/tokenizers/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/text/tokenizers/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/text/utils/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/text/utils/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/vision/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/vision/core/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/core/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/vision/custom_ops/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/custom_ops/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/vision/face_detector/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/face_detector/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/vision/face_detector/proto/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/face_detector/proto/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/vision/face_detector/proto/__pycache__/face_detector_graph_options_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/vision/face_detector/proto/face_detector_graph_options_pb2.py,sha256=euMXdx8V9U64YGy-cZ821c9yVwryjTN3sEXvKbFLpr8,2767
mediapipe/tasks/cc/vision/face_geometry/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/face_geometry/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/vision/face_geometry/calculators/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/face_geometry/calculators/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/vision/face_geometry/calculators/__pycache__/env_generator_calculator_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/vision/face_geometry/calculators/__pycache__/geometry_pipeline_calculator_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/vision/face_geometry/calculators/env_generator_calculator_pb2.py,sha256=QSgEMuBd3uNx5mLo9EAx1P3VP6Ti9Ey4_cOzfn5qzX0,1975
mediapipe/tasks/cc/vision/face_geometry/calculators/geometry_pipeline_calculator_pb2.py,sha256=OwUAJz8wOJuwi7QseFh27Rsi1ktzsoj1YClWzJkpoF0,2194
mediapipe/tasks/cc/vision/face_geometry/data/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/face_geometry/data/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/vision/face_geometry/libs/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/face_geometry/libs/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/vision/face_geometry/proto/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/face_geometry/proto/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/vision/face_geometry/proto/__pycache__/environment_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/vision/face_geometry/proto/__pycache__/face_geometry_graph_options_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/vision/face_geometry/proto/__pycache__/face_geometry_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/vision/face_geometry/proto/__pycache__/geometry_pipeline_metadata_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/vision/face_geometry/proto/__pycache__/mesh_3d_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/vision/face_geometry/proto/environment_pb2.py,sha256=xjfDDcWOtfqSoVuf7bcC6voZl3qp4hhQrpKGJKcCaBA,2109
mediapipe/tasks/cc/vision/face_geometry/proto/face_geometry_graph_options_pb2.py,sha256=TLPdNKLpQUt1Z1dQ8AVrBYzWsXzSvvxpA38vP-zuIMs,2282
mediapipe/tasks/cc/vision/face_geometry/proto/face_geometry_pb2.py,sha256=7hOEltSPJ_VFUNhSaSMHRv61YkBTGVoJj-DR_wLBtyA,1984
mediapipe/tasks/cc/vision/face_geometry/proto/geometry_pipeline_metadata_pb2.py,sha256=oZtpn6XiYQouHRnUmp_BZGh7_1DUljcoguDPxq7SxRA,2546
mediapipe/tasks/cc/vision/face_geometry/proto/mesh_3d_pb2.py,sha256=C9UdlUWseB6L5WePcMj3L0Yap63dFCwiogoJE4fw5pY,1981
mediapipe/tasks/cc/vision/face_landmarker/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/face_landmarker/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/vision/face_landmarker/proto/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/face_landmarker/proto/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/vision/face_landmarker/proto/__pycache__/face_blendshapes_graph_options_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/vision/face_landmarker/proto/__pycache__/face_landmarker_graph_options_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/vision/face_landmarker/proto/__pycache__/face_landmarks_detector_graph_options_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/vision/face_landmarker/proto/__pycache__/tensors_to_face_landmarks_graph_options_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/vision/face_landmarker/proto/face_blendshapes_graph_options_pb2.py,sha256=hX-ugsTxQYpvLD8oxRPFx7Wi-BWF7I1eFWSRjmI0ZMs,2607
mediapipe/tasks/cc/vision/face_landmarker/proto/face_landmarker_graph_options_pb2.py,sha256=waB5AUmjnz_aq3LrloBZtZKsYZUdkXco9PNh7AZ9XSI,4001
mediapipe/tasks/cc/vision/face_landmarker/proto/face_landmarks_detector_graph_options_pb2.py,sha256=NTsyd_VX0eGmBEywcByE-1fJRlEJzHlx-A_d4WN-oyY,3206
mediapipe/tasks/cc/vision/face_landmarker/proto/tensors_to_face_landmarks_graph_options_pb2.py,sha256=NHicmSKBQmBxcolO9wmy7IC9kJ3CcMUswp1gAjUtEwg,2224
mediapipe/tasks/cc/vision/face_stylizer/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/face_stylizer/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/vision/face_stylizer/calculators/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/face_stylizer/calculators/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/vision/face_stylizer/calculators/__pycache__/tensors_to_image_calculator_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/vision/face_stylizer/calculators/tensors_to_image_calculator_pb2.py,sha256=GLlBNnaCBxromIdrYIc_NXHFXxaw61fZmZqEK9n7Ek0,2872
mediapipe/tasks/cc/vision/face_stylizer/proto/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/face_stylizer/proto/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/vision/face_stylizer/proto/__pycache__/face_stylizer_graph_options_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/vision/face_stylizer/proto/face_stylizer_graph_options_pb2.py,sha256=DwGr7QCfKASRa0cRS0FtAk1DUVUJgyYng0Rmt5KEDTM,3107
mediapipe/tasks/cc/vision/gesture_recognizer/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/gesture_recognizer/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/vision/gesture_recognizer/calculators/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/gesture_recognizer/calculators/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/vision/gesture_recognizer/calculators/__pycache__/combined_prediction_calculator_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/vision/gesture_recognizer/calculators/__pycache__/landmarks_to_matrix_calculator_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/vision/gesture_recognizer/calculators/combined_prediction_calculator_pb2.py,sha256=4qLOPQ0fenEx6-jyCZ08zljoIMSfjCUnhdX3G3Sh_uc,2380
mediapipe/tasks/cc/vision/gesture_recognizer/calculators/landmarks_to_matrix_calculator_pb2.py,sha256=z7YWCBSipPtx1_2qkB3NPEIYpb29BJL9OgJ_34hskeo,2016
mediapipe/tasks/cc/vision/gesture_recognizer/proto/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/gesture_recognizer/proto/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/vision/gesture_recognizer/proto/__pycache__/gesture_classifier_graph_options_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/vision/gesture_recognizer/proto/__pycache__/gesture_embedder_graph_options_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/vision/gesture_recognizer/proto/__pycache__/gesture_recognizer_graph_options_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/vision/gesture_recognizer/proto/__pycache__/hand_gesture_recognizer_graph_options_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/vision/gesture_recognizer/proto/gesture_classifier_graph_options_pb2.py,sha256=sJ2pKmpWktanfMSF81WkdXbCs0aIYOCNAzek1xwAiLw,3007
mediapipe/tasks/cc/vision/gesture_recognizer/proto/gesture_embedder_graph_options_pb2.py,sha256=iEpfssjZTocoRhOQMqFg4fpFS8RreMg9ARfY7-MVnec,2613
mediapipe/tasks/cc/vision/gesture_recognizer/proto/gesture_recognizer_graph_options_pb2.py,sha256=5aGORXUJX52154UUzXg843P6g3m7nFP2KSSdzfGlthA,3551
mediapipe/tasks/cc/vision/gesture_recognizer/proto/hand_gesture_recognizer_graph_options_pb2.py,sha256=H0pUoYoJFxeDfTpgPQq9yHVrAcy7mGazoYf2VgnHtxA,3741
mediapipe/tasks/cc/vision/hand_detector/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/hand_detector/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/vision/hand_detector/proto/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/hand_detector/proto/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/vision/hand_detector/proto/__pycache__/hand_detector_graph_options_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/vision/hand_detector/proto/__pycache__/hand_detector_result_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/vision/hand_detector/proto/hand_detector_graph_options_pb2.py,sha256=Xx8xAFYNJ8ga6imaLkQ7tfJthcCeJE9HGTvg1iVriAw,2666
mediapipe/tasks/cc/vision/hand_detector/proto/hand_detector_result_pb2.py,sha256=BCNLwl9_9eTNCZUj510Q_dK7Fc_WCxmPXJe4PWfbsMI,1894
mediapipe/tasks/cc/vision/hand_landmarker/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/hand_landmarker/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/vision/hand_landmarker/calculators/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/hand_landmarker/calculators/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/vision/hand_landmarker/calculators/__pycache__/hand_association_calculator_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/vision/hand_landmarker/calculators/hand_association_calculator_pb2.py,sha256=AYvOWMPKR3GHYxBSzs3ARsZBjwv5GdjHllSNuhmnynI,1928
mediapipe/tasks/cc/vision/hand_landmarker/proto/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/hand_landmarker/proto/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/vision/hand_landmarker/proto/__pycache__/hand_landmarker_graph_options_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/vision/hand_landmarker/proto/__pycache__/hand_landmarks_detector_graph_options_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/vision/hand_landmarker/proto/__pycache__/hand_roi_refinement_graph_options_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/vision/hand_landmarker/proto/hand_landmarker_graph_options_pb2.py,sha256=N0LuwWGX3SEm5CT4O1szqSEyIGRLm4PDhIpTncwvj4A,3536
mediapipe/tasks/cc/vision/hand_landmarker/proto/hand_landmarks_detector_graph_options_pb2.py,sha256=uaMbe7kiAl2sqeDCipZ4w7ia8Ib2PYKwrOi-3eRFfH4,2710
mediapipe/tasks/cc/vision/hand_landmarker/proto/hand_roi_refinement_graph_options_pb2.py,sha256=R3XSHsX1jDtLCKUJhE-MivO-RsVXYj2NIO2NbjfdyYE,1835
mediapipe/tasks/cc/vision/holistic_landmarker/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/holistic_landmarker/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/vision/holistic_landmarker/proto/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/holistic_landmarker/proto/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/vision/holistic_landmarker/proto/__pycache__/holistic_landmarker_graph_options_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/vision/holistic_landmarker/proto/__pycache__/holistic_result_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/vision/holistic_landmarker/proto/holistic_landmarker_graph_options_pb2.py,sha256=DV1yyzLdaHo3VStPMtyawtVx5lnfz-RuNZtbs_2JTw0,4568
mediapipe/tasks/cc/vision/holistic_landmarker/proto/holistic_result_pb2.py,sha256=PtWL7_FwsrEZL4bGDSO5pwFtGmnRDbwuidmacmmBRhw,2409
mediapipe/tasks/cc/vision/image_classifier/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/image_classifier/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/vision/image_classifier/proto/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/image_classifier/proto/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/vision/image_classifier/proto/__pycache__/image_classifier_graph_options_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/vision/image_classifier/proto/image_classifier_graph_options_pb2.py,sha256=HinWQ8GdBf46x5y5ddSFGozra6xeqweXNtRDwazO5Rs,2973
mediapipe/tasks/cc/vision/image_embedder/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/image_embedder/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/vision/image_embedder/proto/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/image_embedder/proto/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/vision/image_embedder/proto/__pycache__/image_embedder_graph_options_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/vision/image_embedder/proto/image_embedder_graph_options_pb2.py,sha256=Etymmsw5FFWVOdJ3h2tG9t2g_Jb2JHCYNR5J0DzlJfo,2943
mediapipe/tasks/cc/vision/image_generator/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/image_generator/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/vision/image_generator/diffuser/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/image_generator/diffuser/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/vision/image_generator/diffuser/__pycache__/stable_diffusion_iterate_calculator_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/vision/image_generator/diffuser/stable_diffusion_iterate_calculator_pb2.py,sha256=afSylLtdIHlRs_iZzMLjLAoFXjpiQ7Oh4bipfJot84U,4168
mediapipe/tasks/cc/vision/image_generator/proto/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/image_generator/proto/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/vision/image_generator/proto/__pycache__/conditioned_image_graph_options_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/vision/image_generator/proto/__pycache__/control_plugin_graph_options_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/vision/image_generator/proto/__pycache__/image_generator_graph_options_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/vision/image_generator/proto/conditioned_image_graph_options_pb2.py,sha256=Ia926M--ofTZBEVLFpqfM_uoHp7BalrwevQMwoRAld0,4339
mediapipe/tasks/cc/vision/image_generator/proto/control_plugin_graph_options_pb2.py,sha256=QGSS-qd4f0nKWZKogTJDarP7xksDtPER1WBQnbv2oFU,2721
mediapipe/tasks/cc/vision/image_generator/proto/image_generator_graph_options_pb2.py,sha256=tI3wFPUzK_MQ6sE-b8RyqiML_BorhCber45D-PlhHZE,2748
mediapipe/tasks/cc/vision/image_segmenter/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/image_segmenter/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/vision/image_segmenter/calculators/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/image_segmenter/calculators/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/vision/image_segmenter/calculators/__pycache__/tensors_to_segmentation_calculator_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/vision/image_segmenter/calculators/tensors_to_segmentation_calculator_pb2.py,sha256=XXGwx9pSK7fucCh-HGrSrw7Xq5t_UuYuTe3xcMZcXiU,2969
mediapipe/tasks/cc/vision/image_segmenter/proto/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/image_segmenter/proto/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/vision/image_segmenter/proto/__pycache__/image_segmenter_graph_options_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/vision/image_segmenter/proto/__pycache__/segmenter_options_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/vision/image_segmenter/proto/image_segmenter_graph_options_pb2.py,sha256=TUjTSpuXqqHDIvaqFuPjtDToWj54Ier94xhs_AEekX4,3020
mediapipe/tasks/cc/vision/image_segmenter/proto/segmenter_options_pb2.py,sha256=d0ILG5ut6k26s6VfJR7hH63QOreBYqeHQSrvurNhG-o,2360
mediapipe/tasks/cc/vision/interactive_segmenter/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/interactive_segmenter/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/vision/object_detector/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/object_detector/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/vision/object_detector/proto/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/object_detector/proto/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/vision/object_detector/proto/__pycache__/object_detector_options_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/vision/object_detector/proto/object_detector_options_pb2.py,sha256=n7lE0bM7wXO-2lr5xAJPavjMwmT7d293nPFvW-MOON8,2935
mediapipe/tasks/cc/vision/pose_detector/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/pose_detector/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/vision/pose_detector/proto/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/pose_detector/proto/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/vision/pose_detector/proto/__pycache__/pose_detector_graph_options_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/vision/pose_detector/proto/pose_detector_graph_options_pb2.py,sha256=yrWl-bK1KVooVl7U0N9HT1Zz5jl-9IjNlzXqRDBr7Xg,2731
mediapipe/tasks/cc/vision/pose_landmarker/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/pose_landmarker/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/vision/pose_landmarker/proto/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/pose_landmarker/proto/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/vision/pose_landmarker/proto/__pycache__/pose_landmarker_graph_options_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/vision/pose_landmarker/proto/__pycache__/pose_landmarks_detector_graph_options_pb2.cpython-311.pyc,,
mediapipe/tasks/cc/vision/pose_landmarker/proto/pose_landmarker_graph_options_pb2.py,sha256=ohWOaLNeFnf_J7OiDqy0gNlkjZCfGsOh1p0tfwhC2q8,3536
mediapipe/tasks/cc/vision/pose_landmarker/proto/pose_landmarks_detector_graph_options_pb2.py,sha256=rADbBYz5u4ZVmfUIWAD651TljZPJXDrPgFzbeKjuMFc,2761
mediapipe/tasks/cc/vision/utils/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/utils/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/cc/vision/utils/ghum/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/tasks/cc/vision/utils/ghum/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/metadata/__pycache__/image_segmenter_metadata_schema_py_generated.cpython-311.pyc,,
mediapipe/tasks/metadata/__pycache__/metadata_schema_py_generated.cpython-311.pyc,,
mediapipe/tasks/metadata/__pycache__/object_detector_metadata_schema_py_generated.cpython-311.pyc,,
mediapipe/tasks/metadata/__pycache__/schema_py_generated.cpython-311.pyc,,
mediapipe/tasks/metadata/image_segmenter_metadata_schema.fbs,sha256=DyxXCWLtfa72rcUMGgt1eyLrFKsbf2d30M0-0hnR7IQ,2308
mediapipe/tasks/metadata/image_segmenter_metadata_schema_py_generated.py,sha256=AAFj_R2lGpo5fDn_ueYxu79CDGQ1lEHp3MqzBiI9TyQ,3509
mediapipe/tasks/metadata/metadata_schema.fbs,sha256=VPDMmah9VgRbwyrRd79dAqKYNkFkk8qz6YoGBXtgeXs,28030
mediapipe/tasks/metadata/metadata_schema_py_generated.py,sha256=vNhXsPWvfXTslI6ILU2OMfS91B3s5WIRDR_8vYRTv1A,117481
mediapipe/tasks/metadata/object_detector_metadata_schema.fbs,sha256=LMhxmCbeYhKjHTZ8bScENy4uoKK_pswxcnp2Y_bITiI,3507
mediapipe/tasks/metadata/object_detector_metadata_schema_py_generated.py,sha256=9qV0Q23rGRKj3NTZuJw23fkNRmsAkC9-WgOZb5b-OZw,24287
mediapipe/tasks/metadata/schema_py_generated.py,sha256=SaJs-QzfUnEnHmH2HUvzopPaR3eCTHPQj1y54OLLwpo,640817
mediapipe/tasks/python/__init__.py,sha256=wIM_WOWboOVI1MeehN8fkN_DjoA0MEBVw5mShAd8AS4,858
mediapipe/tasks/python/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/python/audio/__init__.py,sha256=xHcc-Vb7cZQVbq4Q9X3inHfStMHFDq8KajhFtXRTKHA,1299
mediapipe/tasks/python/audio/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/python/audio/__pycache__/audio_classifier.cpython-311.pyc,,
mediapipe/tasks/python/audio/__pycache__/audio_embedder.cpython-311.pyc,,
mediapipe/tasks/python/audio/audio_classifier.py,sha256=ENGoL8xQs2GJXUOQ9OEfX49xBL_s-CwII_a4SSTvQ6c,14933
mediapipe/tasks/python/audio/audio_embedder.py,sha256=bEqNloS2_njGlJoWXSfCSy0u0A-T892Bxpv5_HRAWoU,13114
mediapipe/tasks/python/audio/core/__init__.py,sha256=ZKC2XRtShVe6k6u6LxDt1pG7DQIn5nZnjurs6Pcvm6A,593
mediapipe/tasks/python/audio/core/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/python/audio/core/__pycache__/audio_record.cpython-311.pyc,,
mediapipe/tasks/python/audio/core/__pycache__/audio_task_running_mode.cpython-311.pyc,,
mediapipe/tasks/python/audio/core/__pycache__/base_audio_task_api.cpython-311.pyc,,
mediapipe/tasks/python/audio/core/audio_record.py,sha256=UwtaXuJUvFp6rTWYKtQzWHarIfUYUD22b_lTMhXQQSs,3655
mediapipe/tasks/python/audio/core/audio_task_running_mode.py,sha256=55Q1aLQSdET8AtzyqjH2DPOsC9qMZCRLzvQUWPyJXqA,1020
mediapipe/tasks/python/audio/core/base_audio_task_api.py,sha256=9n3vj4O1cgD28vMtbUmt0YRACSb3BXW9nPYGSzZDftE,6526
mediapipe/tasks/python/benchmark/__init__.py,sha256=epEucluzX0HinwBZoS7Tgb19j_qgfTuBf-vBkqemch8,587
mediapipe/tasks/python/benchmark/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/python/benchmark/__pycache__/benchmark_utils.cpython-311.pyc,,
mediapipe/tasks/python/benchmark/benchmark_utils.py,sha256=5qbqGxxYlJJQLJzbW0cwMCcGl4c8ZfKL3rNmm7xMVAE,2241
mediapipe/tasks/python/benchmark/vision/__init__.py,sha256=epEucluzX0HinwBZoS7Tgb19j_qgfTuBf-vBkqemch8,587
mediapipe/tasks/python/benchmark/vision/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/python/benchmark/vision/__pycache__/benchmark.cpython-311.pyc,,
mediapipe/tasks/python/benchmark/vision/benchmark.py,sha256=gifRumSkesmXVU51GHRct-UYf8S9Dn2isD28Aw7BClQ,3491
mediapipe/tasks/python/benchmark/vision/core/__init__.py,sha256=ZxHWTuEeRH77CDVcgDbCs5H-B9OomxX7oWZ3YGxG8VM,571
mediapipe/tasks/python/benchmark/vision/core/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/python/benchmark/vision/core/__pycache__/base_vision_benchmark_api.cpython-311.pyc,,
mediapipe/tasks/python/benchmark/vision/core/base_vision_benchmark_api.py,sha256=Yqqje22r4m0pp24m8682xc4kqtZkMlx4VF6zxOiq2b8,1331
mediapipe/tasks/python/components/__init__.py,sha256=KO3IKth2FV2gOlD70fltPxtixGnxyQJksZ-gfrZDQns,587
mediapipe/tasks/python/components/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/python/components/containers/__init__.py,sha256=75hMA8oN_4_cuA-EqO-Mp31-0pMmi-Lt1I3YO1c0jd8,2113
mediapipe/tasks/python/components/containers/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/python/components/containers/__pycache__/audio_data.cpython-311.pyc,,
mediapipe/tasks/python/components/containers/__pycache__/bounding_box.cpython-311.pyc,,
mediapipe/tasks/python/components/containers/__pycache__/category.cpython-311.pyc,,
mediapipe/tasks/python/components/containers/__pycache__/classification_result.cpython-311.pyc,,
mediapipe/tasks/python/components/containers/__pycache__/detections.cpython-311.pyc,,
mediapipe/tasks/python/components/containers/__pycache__/embedding_result.cpython-311.pyc,,
mediapipe/tasks/python/components/containers/__pycache__/keypoint.cpython-311.pyc,,
mediapipe/tasks/python/components/containers/__pycache__/landmark.cpython-311.pyc,,
mediapipe/tasks/python/components/containers/__pycache__/landmark_detection_result.cpython-311.pyc,,
mediapipe/tasks/python/components/containers/__pycache__/rect.cpython-311.pyc,,
mediapipe/tasks/python/components/containers/audio_data.py,sha256=niBVf_WkQgZWqTilV8qAkjlO6JXD_c68uXYoSUlst8w,4504
mediapipe/tasks/python/components/containers/bounding_box.py,sha256=UM0a43BvTp3XPg4OOhfqSsia1PXrwtMjFGLoVco-9ys,2214
mediapipe/tasks/python/components/containers/category.py,sha256=HpCo0FNXdjUeO2Vmyr0wKGkPI5yzGScS_x2Qe8sgvpg,2680
mediapipe/tasks/python/components/containers/classification_result.py,sha256=gYlgYzr2trVn-TK-WxfOPm9X29FXNi8sG7JFEU4V20E,4414
mediapipe/tasks/python/components/containers/detections.py,sha256=pwnH6hyyT4O3fo_W_b-FZEeP6hScxKmfpgyJKWp08Gw,5843
mediapipe/tasks/python/components/containers/embedding_result.py,sha256=kDmSkkfXOjTx_qb-BXG5DAzrN0CjvR9OjmYDt07cFwI,3324
mediapipe/tasks/python/components/containers/keypoint.py,sha256=QhRIJlCIX5W-28X_5xIr5BrMftjsnl4KwMVHkEQLrF4,2406
mediapipe/tasks/python/components/containers/landmark.py,sha256=hns8pbPz0FyAN1yk6bfEtKOXjnDuieP2_F6mVL5xboA,4397
mediapipe/tasks/python/components/containers/landmark_detection_result.py,sha256=CmT7medXmRjTxhr9XSJVd4ERYrG6Xm4VKE02cr51sp0,4112
mediapipe/tasks/python/components/containers/rect.py,sha256=yFhsQnPrfKhFIPd6tuFF3N561EEmPJs6_4mmUzDjLoE,3452
mediapipe/tasks/python/components/processors/__init__.py,sha256=Xo9Iw4VMAwQiV1qrtw7prq-j4I4POyAhzcdkVw_n4Sc,868
mediapipe/tasks/python/components/processors/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/python/components/processors/__pycache__/classifier_options.cpython-311.pyc,,
mediapipe/tasks/python/components/processors/classifier_options.py,sha256=zVQi-vTjJeZaTfniGWhN--Gon0BFirifqzaetFuKv04,3401
mediapipe/tasks/python/components/utils/__init__.py,sha256=KO3IKth2FV2gOlD70fltPxtixGnxyQJksZ-gfrZDQns,587
mediapipe/tasks/python/components/utils/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/python/components/utils/__pycache__/cosine_similarity.cpython-311.pyc,,
mediapipe/tasks/python/components/utils/cosine_similarity.py,sha256=gYdC7Tcce2-HOPy6fTC8vtl64gLJ9kjybxJen90KTVw,2297
mediapipe/tasks/python/core/__init__.py,sha256=KO3IKth2FV2gOlD70fltPxtixGnxyQJksZ-gfrZDQns,587
mediapipe/tasks/python/core/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/python/core/__pycache__/base_options.cpython-311.pyc,,
mediapipe/tasks/python/core/__pycache__/optional_dependencies.cpython-311.pyc,,
mediapipe/tasks/python/core/__pycache__/task_info.cpython-311.pyc,,
mediapipe/tasks/python/core/base_options.py,sha256=tW3Agx9PNt4aQzhhnlaPPZjqVUtyD8M8JEYKvU_fBf8,4028
mediapipe/tasks/python/core/optional_dependencies.py,sha256=Wjh_GT8NVojVSDjB7aHlBc_vjNFsLBi-5JkoVHBeqj8,1061
mediapipe/tasks/python/core/task_info.py,sha256=ceh4-O2U6CprhIIpXAmL3mzfHyomfad6_V8AgDkII6c,5375
mediapipe/tasks/python/genai/__init__.py,sha256=7rri6fT6wNurla8O2c5yKiLs9_3qIY0vKkyVAUDe-18,620
mediapipe/tasks/python/genai/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/python/genai/bundler/__init__.py,sha256=8W-wOVHs9UeOZYJWH1gpsP4CGKDOMHW2LDahLSltpzA,849
mediapipe/tasks/python/genai/bundler/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/python/genai/bundler/__pycache__/llm_bundler.cpython-311.pyc,,
mediapipe/tasks/python/genai/bundler/__pycache__/llm_bundler_test.cpython-311.pyc,,
mediapipe/tasks/python/genai/bundler/llm_bundler.py,sha256=i19DYzNQrs613RkG-DKXBi60AHBvvR5y6vTyIWpgGwc,4684
mediapipe/tasks/python/genai/bundler/llm_bundler_test.py,sha256=pG6ATY2KwnxrNLt5REtx1vLNU7HY4oX_C6DX294xZ6I,5721
mediapipe/tasks/python/genai/converter/__init__.py,sha256=jfUkinDJR5BVldnbJMbo5vIr2Xc5Z4TTnaCJTNoAUvg,893
mediapipe/tasks/python/genai/converter/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/python/genai/converter/__pycache__/converter_base.cpython-311.pyc,,
mediapipe/tasks/python/genai/converter/__pycache__/converter_factory.cpython-311.pyc,,
mediapipe/tasks/python/genai/converter/__pycache__/llm_converter.cpython-311.pyc,,
mediapipe/tasks/python/genai/converter/__pycache__/llm_converter_test.cpython-311.pyc,,
mediapipe/tasks/python/genai/converter/__pycache__/pytorch_converter.cpython-311.pyc,,
mediapipe/tasks/python/genai/converter/__pycache__/pytorch_converter_test.cpython-311.pyc,,
mediapipe/tasks/python/genai/converter/__pycache__/quantization_util.cpython-311.pyc,,
mediapipe/tasks/python/genai/converter/__pycache__/quantization_util_test.cpython-311.pyc,,
mediapipe/tasks/python/genai/converter/__pycache__/safetensors_converter.cpython-311.pyc,,
mediapipe/tasks/python/genai/converter/__pycache__/safetensors_converter_test.cpython-311.pyc,,
mediapipe/tasks/python/genai/converter/__pycache__/weight_bins_writer.cpython-311.pyc,,
mediapipe/tasks/python/genai/converter/__pycache__/weight_bins_writer_test.cpython-311.pyc,,
mediapipe/tasks/python/genai/converter/converter_base.py,sha256=kz_TeQlknJDa6kSSDtOyjgiapSkjWorVjKzCu0VmkZY,6621
mediapipe/tasks/python/genai/converter/converter_factory.py,sha256=2K16PZBQym0WhXM2HOdBMHMugykohoD4OTaOIo-UKko,2928
mediapipe/tasks/python/genai/converter/llm_converter.py,sha256=3RFtu3MPSCqe216DGHbM3hQDTTpr9U3zHQOZvvRtEIg,14321
mediapipe/tasks/python/genai/converter/llm_converter_test.py,sha256=33sQ8F3-YKmLbwMIvis1t_1kSZnRLMR6351oWefONm8,1995
mediapipe/tasks/python/genai/converter/pytorch_converter.py,sha256=b-GWYOzgD-ZRGgyqcXE9LG_JOL0Mqba4q0pc_imrbrg,10771
mediapipe/tasks/python/genai/converter/pytorch_converter_test.py,sha256=y_Mg9pOQtlUDh6uVmkz5LcUbk-pmDLA9L3KcxKR-OaA,3041
mediapipe/tasks/python/genai/converter/quantization_util.py,sha256=p9n7FmIE3QkrlDdiQ67P6h7-IJIxw4FUCpqSMBLtM8Q,17195
mediapipe/tasks/python/genai/converter/quantization_util_test.py,sha256=ICujhTFeREGuHGmNk1PlBpf1AUThFvv-Wl5UuZ-xWAk,9060
mediapipe/tasks/python/genai/converter/safetensors_converter.py,sha256=OU5o3RsmR2ZWQNQs2MpPba9AaFIT32jfbJa3OEQe16Q,20757
mediapipe/tasks/python/genai/converter/safetensors_converter_test.py,sha256=oCk4FnsjBJkEPlXtv8fdq9dn3I06LsSQRMi0BV_9mew,2802
mediapipe/tasks/python/genai/converter/weight_bins_writer.py,sha256=iu0u4W808AUzelpEOXd7RvIQMDJiKsHgXJ0U6z03xmU,4580
mediapipe/tasks/python/genai/converter/weight_bins_writer_test.py,sha256=z1qIqT4ioWKCT54t8y3Iy4gs36BBqnFw4DyO5yJzZMw,3069
mediapipe/tasks/python/metadata/__init__.py,sha256=YGHXQMz1ZGPcNgSXggu03b0USZKE8d9Xqvn6NDUl898,586
mediapipe/tasks/python/metadata/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/python/metadata/__pycache__/metadata.cpython-311.pyc,,
mediapipe/tasks/python/metadata/__pycache__/metadata_displayer_cli.cpython-311.pyc,,
mediapipe/tasks/python/metadata/flatbuffers_lib/_pywrap_flatbuffers.cpython-311-darwin.so,sha256=jjRx-yfLao4oLoCTTBAjxA27FDPRi8z_tKknV5uODeY,1567560
mediapipe/tasks/python/metadata/flatbuffers_lib/_pywrap_flatbuffers/arm64.cpython-311-darwin.so,sha256=Fg1BENk8L-ITRJueAuzHdqIzH9JoOnzI4OHvH1oWuC0,4152
mediapipe/tasks/python/metadata/metadata.py,sha256=EECQnM-Af0angD60jaBBOuNMgt7HExH6SqVtVMFNHGc,33763
mediapipe/tasks/python/metadata/metadata_displayer_cli.py,sha256=tLhF0B1mXG0igFTA9nPh8t1efRpRw2hQ00XpTPYdk_o,1202
mediapipe/tasks/python/metadata/metadata_writers/__init__.py,sha256=YGHXQMz1ZGPcNgSXggu03b0USZKE8d9Xqvn6NDUl898,586
mediapipe/tasks/python/metadata/metadata_writers/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/python/metadata/metadata_writers/__pycache__/face_stylizer.cpython-311.pyc,,
mediapipe/tasks/python/metadata/metadata_writers/__pycache__/image_classifier.cpython-311.pyc,,
mediapipe/tasks/python/metadata/metadata_writers/__pycache__/image_segmenter.cpython-311.pyc,,
mediapipe/tasks/python/metadata/metadata_writers/__pycache__/metadata_info.cpython-311.pyc,,
mediapipe/tasks/python/metadata/metadata_writers/__pycache__/metadata_writer.cpython-311.pyc,,
mediapipe/tasks/python/metadata/metadata_writers/__pycache__/model_asset_bundle_utils.cpython-311.pyc,,
mediapipe/tasks/python/metadata/metadata_writers/__pycache__/object_detector.cpython-311.pyc,,
mediapipe/tasks/python/metadata/metadata_writers/__pycache__/text_classifier.cpython-311.pyc,,
mediapipe/tasks/python/metadata/metadata_writers/__pycache__/writer_utils.cpython-311.pyc,,
mediapipe/tasks/python/metadata/metadata_writers/face_stylizer.py,sha256=BgbMpP-kVez_FxqBUxUMgysFZRO23xBOZIkgwnuI-KA,5502
mediapipe/tasks/python/metadata/metadata_writers/image_classifier.py,sha256=fU4Xu4bm0fak8h7yjVAAQBIHpEH18ZEiXUSc-2dHgvk,3023
mediapipe/tasks/python/metadata/metadata_writers/image_segmenter.py,sha256=UAR6lvEaOa4_8Xg7HktJljuOOZrci2fI0_JDU6Na0OU,6358
mediapipe/tasks/python/metadata/metadata_writers/metadata_info.py,sha256=I9PjRWbU4-AiDO9dRx353RFDIWtvGit87aULDhJuT34,45392
mediapipe/tasks/python/metadata/metadata_writers/metadata_writer.py,sha256=v-T-qD0J6LBfV2IGYwCHm18L2RXcYrIdDG1LYb6GitY,31826
mediapipe/tasks/python/metadata/metadata_writers/model_asset_bundle_utils.py,sha256=HMUhrYzAJ0lQCAfKDOTQ82_EGDJfVS-ECVdZfFQGDM4,2622
mediapipe/tasks/python/metadata/metadata_writers/object_detector.py,sha256=IzKqY1S0p2RQ_Vd0nF08y16dHYLa-2HwB4zmuWmixVw,12970
mediapipe/tasks/python/metadata/metadata_writers/text_classifier.py,sha256=4tsw2b9MrsoHg3rFODT048xEUe_-t_HWzJuziM6au9E,5558
mediapipe/tasks/python/metadata/metadata_writers/writer_utils.py,sha256=1idd3qaXqIBpC7uTowbqM_3zOHWy_YPjiitOOfUuQdk,3094
mediapipe/tasks/python/test/__init__.py,sha256=KO3IKth2FV2gOlD70fltPxtixGnxyQJksZ-gfrZDQns,587
mediapipe/tasks/python/test/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/python/test/__pycache__/test_utils.cpython-311.pyc,,
mediapipe/tasks/python/test/audio/__init__.py,sha256=KO3IKth2FV2gOlD70fltPxtixGnxyQJksZ-gfrZDQns,587
mediapipe/tasks/python/test/audio/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/python/test/audio/__pycache__/audio_classifier_test.cpython-311.pyc,,
mediapipe/tasks/python/test/audio/__pycache__/audio_embedder_test.cpython-311.pyc,,
mediapipe/tasks/python/test/audio/audio_classifier_test.py,sha256=woyqErwUd2aErsySWs-wMezJZr0_kVpq5aDkJYY7Wh0,18571
mediapipe/tasks/python/test/audio/audio_embedder_test.py,sha256=c9-pLi1rNzawy9r2MTjS730JOkBBokrB0Drhssx2qzo,13347
mediapipe/tasks/python/test/test_utils.py,sha256=6fT5lCi8AtTEr44E1Ja4-tf4FYR7d89khv0DZsvbU_c,7122
mediapipe/tasks/python/test/text/__init__.py,sha256=KO3IKth2FV2gOlD70fltPxtixGnxyQJksZ-gfrZDQns,587
mediapipe/tasks/python/test/text/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/python/test/text/__pycache__/language_detector_test.cpython-311.pyc,,
mediapipe/tasks/python/test/text/__pycache__/text_classifier_test.cpython-311.pyc,,
mediapipe/tasks/python/test/text/__pycache__/text_embedder_test.cpython-311.pyc,,
mediapipe/tasks/python/test/text/language_detector_test.py,sha256=7SIXxZHJlVOsiFnrrngSgy3PHcejjRm4oBoK8CbOI5U,8857
mediapipe/tasks/python/test/text/text_classifier_test.py,sha256=y_rj-H0I6PC3AlTtxdTovtz8MTWpwjsWT1Qs91NrbHQ,9303
mediapipe/tasks/python/test/text/text_embedder_test.py,sha256=ezQjLKelg075kO9JQpjJ4C7ywhfiPHgEu4UmXxCiYYI,11107
mediapipe/tasks/python/test/vision/__init__.py,sha256=KO3IKth2FV2gOlD70fltPxtixGnxyQJksZ-gfrZDQns,587
mediapipe/tasks/python/test/vision/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/python/test/vision/__pycache__/face_aligner_test.cpython-311.pyc,,
mediapipe/tasks/python/test/vision/__pycache__/face_detector_test.cpython-311.pyc,,
mediapipe/tasks/python/test/vision/__pycache__/face_landmarker_test.cpython-311.pyc,,
mediapipe/tasks/python/test/vision/__pycache__/face_stylizer_test.cpython-311.pyc,,
mediapipe/tasks/python/test/vision/__pycache__/hand_landmarker_test.cpython-311.pyc,,
mediapipe/tasks/python/test/vision/__pycache__/holistic_landmarker_test.cpython-311.pyc,,
mediapipe/tasks/python/test/vision/__pycache__/image_classifier_test.cpython-311.pyc,,
mediapipe/tasks/python/test/vision/__pycache__/image_embedder_test.cpython-311.pyc,,
mediapipe/tasks/python/test/vision/__pycache__/image_segmenter_test.cpython-311.pyc,,
mediapipe/tasks/python/test/vision/__pycache__/interactive_segmenter_test.cpython-311.pyc,,
mediapipe/tasks/python/test/vision/__pycache__/object_detector_test.cpython-311.pyc,,
mediapipe/tasks/python/test/vision/__pycache__/pose_landmarker_test.cpython-311.pyc,,
mediapipe/tasks/python/test/vision/face_aligner_test.py,sha256=13z5DyOGJMG6gFl_hEKExXdwvIVMmqS4M3WLgNiU9b8,7595
mediapipe/tasks/python/test/vision/face_detector_test.py,sha256=VtQzPz5WFH4T9UDcVE2XwXfAd-NrHIv7mmGYFp5WJIQ,19645
mediapipe/tasks/python/test/vision/face_landmarker_test.py,sha256=w10GorD-W0Dyltj6sjZJnPoG3Z7wKcgHeR_rv4UJ8WA,21646
mediapipe/tasks/python/test/vision/face_stylizer_test.py,sha256=e4cBXaA_4a2x5XbmL_V__6fKgL-GJiJbkm-BBijnovQ,7704
mediapipe/tasks/python/test/vision/hand_landmarker_test.py,sha256=XwT5dyBC-0a5ELphviJh8Ui7ct06FMmgcHS5gM-4l5M,20258
mediapipe/tasks/python/test/vision/holistic_landmarker_test.py,sha256=T-uaLFP9lCX9aUqMWJOf8WLjKB2a1AFVNDk7EP5X8jM,20237
mediapipe/tasks/python/test/vision/image_classifier_test.py,sha256=JtYzO1N1kqv-fHVsZjAQv-WEdkLrhDiRZly5AGkX7L0,25249
mediapipe/tasks/python/test/vision/image_embedder_test.py,sha256=o7FJoICkRH5SL3bRIRUPL8feHrzIgrB7ZdBFgkRtP3Y,18218
mediapipe/tasks/python/test/vision/image_segmenter_test.py,sha256=xASHgafwnSuMqomcEBLRPdm3A--P3j6vWKIOr8Jbmrk,18824
mediapipe/tasks/python/test/vision/interactive_segmenter_test.py,sha256=6jtBLO6MDsFrpyWVYtHX1DpgZUkcTKBiGRPXMHE1Q3o,11999
mediapipe/tasks/python/test/vision/object_detector_test.py,sha256=O7TIKI4WgJQ3LCWpHVmPbEZ3vkXq7YpUb--7nvOXgUY,18469
mediapipe/tasks/python/test/vision/pose_landmarker_test.py,sha256=vntLpLehzTqn25PL72jpnBYxAdCxOYAnGZNGBJ9TbsI,19223
mediapipe/tasks/python/text/__init__.py,sha256=OLwFd4sxzE9zGLgle7r2z1n12V0GD1fcvmu1XqxrrYg,1423
mediapipe/tasks/python/text/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/python/text/__pycache__/language_detector.cpython-311.pyc,,
mediapipe/tasks/python/text/__pycache__/text_classifier.cpython-311.pyc,,
mediapipe/tasks/python/text/__pycache__/text_embedder.cpython-311.pyc,,
mediapipe/tasks/python/text/core/__init__.py,sha256=ZKC2XRtShVe6k6u6LxDt1pG7DQIn5nZnjurs6Pcvm6A,593
mediapipe/tasks/python/text/core/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/python/text/core/__pycache__/base_text_task_api.cpython-311.pyc,,
mediapipe/tasks/python/text/core/base_text_task_api.py,sha256=OHt7j_0n5c3HBdOrCb_BGWCdKWMKvDULp6tKA5mDZAc,1822
mediapipe/tasks/python/text/language_detector.py,sha256=VwJAKSJ85tmHjPw7riz9oo846lyUwDGQATVMUD5T0CU,8244
mediapipe/tasks/python/text/text_classifier.py,sha256=AJbYep6iL8vkf6JKRrGArr9sNd5ugLEliOK1tNr3DsE,7917
mediapipe/tasks/python/text/text_embedder.py,sha256=JB-jQyVgcbfH8tOuJjV2Qyk2ewyUn7bCSBrl9QGBZ1Y,7300
mediapipe/tasks/python/vision/__init__.py,sha256=Ib_UshmEOSNW0TjDFywXs2R4l6C7A_W4tbaKjcEIxqY,4162
mediapipe/tasks/python/vision/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/python/vision/__pycache__/face_aligner.cpython-311.pyc,,
mediapipe/tasks/python/vision/__pycache__/face_detector.cpython-311.pyc,,
mediapipe/tasks/python/vision/__pycache__/face_landmarker.cpython-311.pyc,,
mediapipe/tasks/python/vision/__pycache__/face_stylizer.cpython-311.pyc,,
mediapipe/tasks/python/vision/__pycache__/gesture_recognizer.cpython-311.pyc,,
mediapipe/tasks/python/vision/__pycache__/hand_landmarker.cpython-311.pyc,,
mediapipe/tasks/python/vision/__pycache__/holistic_landmarker.cpython-311.pyc,,
mediapipe/tasks/python/vision/__pycache__/image_classifier.cpython-311.pyc,,
mediapipe/tasks/python/vision/__pycache__/image_embedder.cpython-311.pyc,,
mediapipe/tasks/python/vision/__pycache__/image_segmenter.cpython-311.pyc,,
mediapipe/tasks/python/vision/__pycache__/interactive_segmenter.cpython-311.pyc,,
mediapipe/tasks/python/vision/__pycache__/object_detector.cpython-311.pyc,,
mediapipe/tasks/python/vision/__pycache__/pose_landmarker.cpython-311.pyc,,
mediapipe/tasks/python/vision/core/__init__.py,sha256=sVJS2p8J2PNVl8DLRPVY6KLpHenP_z3QVPRU0x_iL5g,571
mediapipe/tasks/python/vision/core/__pycache__/__init__.cpython-311.pyc,,
mediapipe/tasks/python/vision/core/__pycache__/base_vision_task_api.cpython-311.pyc,,
mediapipe/tasks/python/vision/core/__pycache__/image_processing_options.cpython-311.pyc,,
mediapipe/tasks/python/vision/core/__pycache__/vision_task_running_mode.cpython-311.pyc,,
mediapipe/tasks/python/vision/core/base_vision_task_api.py,sha256=BMqmHzlCHwga4w2P9tlXjR3uNQxfDgpbGZCW31m_eeU,8490
mediapipe/tasks/python/vision/core/image_processing_options.py,sha256=A2HPjQWO13g0ITpIIhmcJrjklaNsi7Y0qxV97ZjOk7w,1529
mediapipe/tasks/python/vision/core/vision_task_running_mode.py,sha256=RcQM54W3QWn4dByl-lJ1OtX8ede5Pt0P6m9aYlR2eoU,1123
mediapipe/tasks/python/vision/face_aligner.py,sha256=YlxO9uChuNR-yoQTCaGeXuWxHV_mP1AKZ3E4OBI-Udo,5827
mediapipe/tasks/python/vision/face_detector.py,sha256=Swt0AOBsDh5oPhlZMvA3m-Exw3CEh01qHtIKkfEhnbw,13259
mediapipe/tasks/python/vision/face_landmarker.py,sha256=dmh4mJT3DnB07PHWB2p7fqe2x0AgJ2b8Za-bK1B5GV4,94096
mediapipe/tasks/python/vision/face_stylizer.py,sha256=ToAhk1GZYX7xvxeGtI_a-yhlWlVsey_8CZWjNW9vltA,5761
mediapipe/tasks/python/vision/gesture_recognizer.py,sha256=8c7bGWhCaZ6wuMu0Z34OPsuKn_3He1S12m8NMfCgjns,19258
mediapipe/tasks/python/vision/hand_landmarker.py,sha256=1FgMzcN7HBJylK1cflk4h9rr7_GQ9AH2s7AlDLGMqaQ,18243
mediapipe/tasks/python/vision/holistic_landmarker.py,sha256=ES4AefmYfebHDPB0_TB7PaW6AYgzfAI-vCbt2BucSHs,23559
mediapipe/tasks/python/vision/image_classifier.py,sha256=sZuHU5SiF_oPnKKBZwzGYT5tILzA6xn90-IoXnsz7oc,14946
mediapipe/tasks/python/vision/image_embedder.py,sha256=0c7FDnUO7_wRNcCGYvhIpYI0q5HB4bpmCjNR7uj7Ec0,14383
mediapipe/tasks/python/vision/image_segmenter.py,sha256=LjjbWje4xjlE-aPj-oNFirz_7zAdXV52-NpykEV5_v0,16730
mediapipe/tasks/python/vision/interactive_segmenter.py,sha256=f3GIHmeHivVMTBSNf-lBrPsG-y0nBW2UFGwcZk5F8U4,10462
mediapipe/tasks/python/vision/object_detector.py,sha256=aKwDk3uSq8w4CBXWDaek3ujtvbwTG1ZUswOKIwUlyUY,16420
mediapipe/tasks/python/vision/pose_landmarker.py,sha256=6QCKc7NDoNSqhasXBpXbuVUuE5hEwyXx4-fFC71gnjs,16960
mediapipe/util/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/util/__pycache__/__init__.cpython-311.pyc,,
mediapipe/util/__pycache__/audio_decoder_pb2.cpython-311.pyc,,
mediapipe/util/__pycache__/color_pb2.cpython-311.pyc,,
mediapipe/util/__pycache__/label_map_pb2.cpython-311.pyc,,
mediapipe/util/__pycache__/render_data_pb2.cpython-311.pyc,,
mediapipe/util/analytics/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/util/analytics/__pycache__/__init__.cpython-311.pyc,,
mediapipe/util/analytics/__pycache__/mediapipe_log_extension_pb2.cpython-311.pyc,,
mediapipe/util/analytics/__pycache__/mediapipe_logging_enums_pb2.cpython-311.pyc,,
mediapipe/util/analytics/mediapipe_log_extension_pb2.py,sha256=-v5-yTkDoEOKNHV-jkjwnzpU0GtTw97h0IWeIAAoEro,4720
mediapipe/util/analytics/mediapipe_logging_enums_pb2.py,sha256=WlDWerFEZEcGFlJ17fxKcf-f8UZB2L-7uR1to6WEn_g,3781
mediapipe/util/audio_decoder_pb2.py,sha256=9I7uRTVzNTZYslbzlug1RwLNxq1SrAsqeIqtsmueDlY,2279
mediapipe/util/color_pb2.py,sha256=msoLkPsTsP3S6vLy4F4kaoVVOhk3wQKjf1l3yeiRkGA,1869
mediapipe/util/label_map_pb2.py,sha256=1-uAppBx2NrC6_frVxWNYWeIPolcQhik5GjvQKWEOe4,1328
mediapipe/util/render_data_pb2.py,sha256=8FjnXPbC2lJkJ8eWsd7mDtp4XKQi1CxAOFYIK6ijlF0,7838
mediapipe/util/sequence/__init__.py,sha256=bglKd2k2C7QGT1i-vstURXPJX2Cvq9FO9opr6cVeBp0,571
mediapipe/util/sequence/__pycache__/__init__.cpython-311.pyc,,
mediapipe/util/sequence/__pycache__/media_sequence.cpython-311.pyc,,
mediapipe/util/sequence/__pycache__/media_sequence_test.cpython-311.pyc,,
mediapipe/util/sequence/__pycache__/media_sequence_util.cpython-311.pyc,,
mediapipe/util/sequence/__pycache__/media_sequence_util_test.cpython-311.pyc,,
mediapipe/util/sequence/media_sequence.py,sha256=1-6E4J_Y8LsnBB7v_U2gQrVhi8oE-0r7aeuUBVH5Jm8,36596
mediapipe/util/sequence/media_sequence_test.py,sha256=pJ3YPLurEtS9t_PymjQCZrjDArchN_MGpvWYALRjwng,13823
mediapipe/util/sequence/media_sequence_util.py,sha256=OTj8ZHKRWTxI08ko0p_9U2L9SR3hXQhYD_mSDER40zE,27189
mediapipe/util/sequence/media_sequence_util_test.py,sha256=nuN9-HW3kw2kZbraCH76qhbaSyrPYZ3Fi_lXW9PvyZE,18180
mediapipe/util/tracking/__init__.py,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
mediapipe/util/tracking/__pycache__/__init__.cpython-311.pyc,,
mediapipe/util/tracking/__pycache__/box_detector_pb2.cpython-311.pyc,,
mediapipe/util/tracking/__pycache__/box_tracker_pb2.cpython-311.pyc,,
mediapipe/util/tracking/__pycache__/camera_motion_pb2.cpython-311.pyc,,
mediapipe/util/tracking/__pycache__/flow_packager_pb2.cpython-311.pyc,,
mediapipe/util/tracking/__pycache__/frame_selection_pb2.cpython-311.pyc,,
mediapipe/util/tracking/__pycache__/frame_selection_solution_evaluator_pb2.cpython-311.pyc,,
mediapipe/util/tracking/__pycache__/motion_analysis_pb2.cpython-311.pyc,,
mediapipe/util/tracking/__pycache__/motion_estimation_pb2.cpython-311.pyc,,
mediapipe/util/tracking/__pycache__/motion_models_pb2.cpython-311.pyc,,
mediapipe/util/tracking/__pycache__/motion_saliency_pb2.cpython-311.pyc,,
mediapipe/util/tracking/__pycache__/push_pull_filtering_pb2.cpython-311.pyc,,
mediapipe/util/tracking/__pycache__/region_flow_computation_pb2.cpython-311.pyc,,
mediapipe/util/tracking/__pycache__/region_flow_pb2.cpython-311.pyc,,
mediapipe/util/tracking/__pycache__/tone_estimation_pb2.cpython-311.pyc,,
mediapipe/util/tracking/__pycache__/tone_models_pb2.cpython-311.pyc,,
mediapipe/util/tracking/__pycache__/tracked_detection_manager_config_pb2.cpython-311.pyc,,
mediapipe/util/tracking/__pycache__/tracking_pb2.cpython-311.pyc,,
mediapipe/util/tracking/box_detector_pb2.py,sha256=vCO_lMSDO3j18Wp9pXyEX0T6Ct5X-nrmvijXudFvOmk,3726
mediapipe/util/tracking/box_tracker_pb2.py,sha256=xsaVNLUUo9-********************************,2764
mediapipe/util/tracking/camera_motion_pb2.py,sha256=7y5nhmp0GTZ5tM7GEjlgykFB5HR2OIYK2y1R7-XTiX8,3880
mediapipe/util/tracking/flow_packager_pb2.py,sha256=mhNVgHJ-CjwKVJfPB_78OCBC007rwttf3Xhna5dtjNs,6732
mediapipe/util/tracking/frame_selection_pb2.py,sha256=PQxVftbahPeBo8GuWhHTnpjcKCSbJaSG9oIsvBioZtE,3027
mediapipe/util/tracking/frame_selection_solution_evaluator_pb2.py,sha256=kuz1cnf5CvvbPobz3LM72lkuEi8LaSM876XOEEhRumE,1619
mediapipe/util/tracking/motion_analysis_pb2.py,sha256=VmJiG3Triga-ozBXKcTjKZBpoS2qusmE1qIc1eB8mok,4281
mediapipe/util/tracking/motion_estimation_pb2.py,sha256=agwlp2OU8xLbI4zHunYMSagXk9RQZ2gYBFrZ7beKzjk,15326
mediapipe/util/tracking/motion_models_pb2.py,sha256=b8hGBuH3Qy20ZGXnREGoA_2pyMx8x3qo-nX6oMrLvlg,3672
mediapipe/util/tracking/motion_saliency_pb2.py,sha256=cQH9_2LgnB5pf8bMxDQsEd8SjrcLOmtk8KxwD-b-rWg,2081
mediapipe/util/tracking/push_pull_filtering_pb2.py,sha256=qLNWQMpNh3jrdNKaiCQZGOg6q3UkIlErTSgo0Im3tKg,1431
mediapipe/util/tracking/region_flow_computation_pb2.py,sha256=UVAE7zAJwGetMmYuPu0RI0hDNr8bSvE7SOaQkArLKNg,13085
mediapipe/util/tracking/region_flow_pb2.py,sha256=yvNIR4zp_lRiFlhhPX78opMu14UNP4zuO-LCwI5aN38,6613
mediapipe/util/tracking/tone_estimation_pb2.py,sha256=IqGR6MOPj61KTRWROn_OWxa3CPyh2oXGRqp3jCWeqLQ,5782
mediapipe/util/tracking/tone_models_pb2.py,sha256=IDsMwiKPSovDl_-KSP_ZZ9wCbUV4feDhNCyPB5pjrtg,2479
mediapipe/util/tracking/tracked_detection_manager_config_pb2.py,sha256=ij5Jvz9eUQAluO28SYuJLTFr5M84GyAsAhgoB3sjL8E,1323
mediapipe/util/tracking/tracking_pb2.py,sha256=hFC2FdGqXS5NhAUWAABQX5HKbyDlJc3VUZCZeTzib_w,11536
mediapipe/version.txt,,
