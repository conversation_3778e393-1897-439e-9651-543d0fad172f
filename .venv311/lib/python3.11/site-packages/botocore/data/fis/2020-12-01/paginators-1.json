{"pagination": {"ListActions": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "actions"}, "ListExperimentResolvedTargets": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "resolvedTargets"}, "ListExperimentTemplates": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "experimentTemplates"}, "ListExperiments": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "experiments"}, "ListTargetAccountConfigurations": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "targetAccountConfigurations"}, "ListTargetResourceTypes": {"input_token": "nextToken", "output_token": "nextToken", "limit_key": "maxResults", "result_key": "targetResourceTypes"}}}