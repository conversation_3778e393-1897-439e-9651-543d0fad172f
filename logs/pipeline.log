2025-09-06 12:51:25,951 - lipreading.s3 - INFO - ✅ Connected to S3 bucket: icudatasetphrasesfortesting
2025-09-06 12:53:36,448 - lipreading.s3 - INFO - ✅ Connected to S3 bucket: icudatasetphrasesfortesting
2025-09-06 12:53:36,449 - lipreading - INFO - 🚀 Starting manifest building process...
2025-09-06 12:53:36,449 - lipreading - INFO - 🔍 Enumerating S3 videos...
2025-09-06 12:53:40,294 - lipreading.s3 - INFO - 📹 Found 15788 videos in s3://icudatasetphrasesfortesting/icu-videos/
2025-09-06 12:57:28,109 - lipreading - INFO - ✅ Found 2322 valid videos for target phrases
2025-09-06 12:57:28,110 - lipreading - INFO - 📊 Analyzing speaker and phrase distribution...
2025-09-06 12:57:28,114 - lipreading - INFO - 🔀 Creating speaker-disjoint splits...
2025-09-06 12:57:28,123 - lipreading - INFO - 💾 Saving manifests...
2025-09-06 12:57:28,151 - lipreading - INFO - ✅ Saved manifests to ./manifests/
2025-09-06 12:57:28,152 - lipreading - INFO - 🎉 Manifest building completed successfully!
2025-09-06 12:57:43,837 - lipreading.s3 - INFO - ✅ Connected to S3 bucket: icudatasetphrasesfortesting
2025-09-06 12:57:43,838 - lipreading - INFO - 🤖 Loading YOLO and SAM models...
2025-09-06 12:57:44,196 - lipreading - INFO - ✅ Models loaded successfully
2025-09-06 12:57:44,196 - lipreading - INFO - 📋 Processing videos from manifest: manifests/train.csv
2025-09-06 12:57:44,201 - lipreading - INFO - 🔢 Limiting to 2 samples per class
2025-09-06 12:57:44,205 - lipreading - INFO - 📊 Limited dataset size: 20 samples
2025-09-06 13:01:30,004 - lipreading - ERROR - ❌ Error processing icu-videos/65plus/female/caucasian/i_m_hot/i_m_hot__useruser01__65plus__female__caucasian__20250827T062341.webm: setting an array element with a sequence. The requested array has an inhomogeneous shape after 1 dimensions. The detected shape was (104,) + inhomogeneous part.
2025-09-06 13:03:47,785 - lipreading.s3 - INFO - ✅ Connected to S3 bucket: icudatasetphrasesfortesting
2025-09-06 13:03:47,785 - lipreading - INFO - 🤖 Loading YOLO and SAM models...
2025-09-06 13:03:48,159 - lipreading - INFO - ✅ SAM model loaded
2025-09-06 13:03:48,159 - lipreading - INFO - ✅ Models loaded successfully
2025-09-06 13:03:48,159 - lipreading - INFO - 📋 Processing videos from manifest: manifests/train.csv
2025-09-06 13:03:48,163 - lipreading - INFO - 🔢 Limiting to 2 samples per class
2025-09-06 13:03:48,164 - lipreading - INFO - 📊 Limited dataset size: 20 samples
2025-09-06 13:07:32,239 - lipreading - ERROR - ❌ Error processing icu-videos/65plus/female/caucasian/i_m_hot/i_m_hot__useruser01__65plus__female__caucasian__20250827T062341.webm: setting an array element with a sequence. The requested array has an inhomogeneous shape after 1 dimensions. The detected shape was (104,) + inhomogeneous part.
2025-09-06 13:09:06,205 - lipreading.s3 - INFO - ✅ Connected to S3 bucket: icudatasetphrasesfortesting
2025-09-06 13:09:06,205 - lipreading - INFO - 🤖 Loading YOLO and SAM models...
2025-09-06 13:09:06,251 - lipreading - WARNING - ⚠️ SAM checkpoint not found: ./checkpoints/sam_vit_b_01ec64.pth
2025-09-06 13:09:06,251 - lipreading - INFO - ✅ Models loaded successfully
2025-09-06 13:09:06,251 - lipreading - INFO - 📋 Processing videos from manifest: manifests/train.csv
2025-09-06 13:09:06,256 - lipreading - INFO - 🔢 Limiting to 2 samples per class
2025-09-06 13:09:06,258 - lipreading - INFO - 📊 Limited dataset size: 20 samples
2025-09-06 13:09:09,107 - lipreading - ERROR - ❌ Error processing icu-videos/65plus/female/caucasian/i_m_hot/i_m_hot__useruser01__65plus__female__caucasian__20250827T062341.webm: setting an array element with a sequence. The requested array has an inhomogeneous shape after 1 dimensions. The detected shape was (104,) + inhomogeneous part.
2025-09-06 13:09:10,810 - lipreading - ERROR - ❌ Error processing icu-videos/40to64/female/caucasian/i_m_hot/i_m_hot__useruser01__40to64__female__caucasian__20250827T110113.webm: setting an array element with a sequence. The requested array has an inhomogeneous shape after 1 dimensions. The detected shape was (65,) + inhomogeneous part.
2025-09-06 13:09:13,715 - lipreading - ERROR - ❌ Error processing icu-videos/40to64/male/asian/doctor/doctor__useruser01__40to64__male__asian__20250905T072233.webm: setting an array element with a sequence. The requested array has an inhomogeneous shape after 1 dimensions. The detected shape was (117,) + inhomogeneous part.
2025-09-06 13:09:15,129 - lipreading - ERROR - ❌ Error processing icu-videos/65plus/female/caucasian/doctor/doctor__useruser01__65plus__female__caucasian__20250827T055549.webm: setting an array element with a sequence. The requested array has an inhomogeneous shape after 1 dimensions. The detected shape was (51,) + inhomogeneous part.
2025-09-06 13:09:17,192 - lipreading - ERROR - ❌ Error processing icu-videos/65plus/female/caucasian/help/help__useruser01__65plus__female__caucasian__20250827T060054.webm: setting an array element with a sequence. The requested array has an inhomogeneous shape after 1 dimensions. The detected shape was (80,) + inhomogeneous part.
2025-09-06 13:09:20,510 - lipreading - ERROR - ❌ Error processing icu-videos/65plus/female/asian/help/help__useruser01__65plus__female__asian__20250730T032711.webm: setting an array element with a sequence. The requested array has an inhomogeneous shape after 1 dimensions. The detected shape was (137,) + inhomogeneous part.
2025-09-06 13:09:24,403 - lipreading - ERROR - ❌ Error processing icu-videos/65plus/female/caucasian/pillow/pillow__useruser01__65plus__female__caucasian__20250716T052824.webm: setting an array element with a sequence. The requested array has an inhomogeneous shape after 1 dimensions. The detected shape was (86,) + inhomogeneous part.
2025-09-06 13:09:27,587 - lipreading - ERROR - ❌ Error processing icu-videos/65plus/female/caucasian/my_chest_hurts/my_chest_hurts__useruser01__65plus__female__caucasian__20250827T055033.webm: setting an array element with a sequence. The requested array has an inhomogeneous shape after 1 dimensions. The detected shape was (125,) + inhomogeneous part.
2025-09-06 13:09:30,730 - lipreading - ERROR - ❌ Error processing icu-videos/65plus/female/caucasian/my_chest_hurts/my_chest_hurts__useruser01__65plus__female__caucasian__20250827T060328.webm: setting an array element with a sequence. The requested array has an inhomogeneous shape after 1 dimensions. The detected shape was (128,) + inhomogeneous part.
2025-09-06 13:09:34,360 - lipreading - ERROR - ❌ Error processing icu-videos/18to39/male/caucasian/my_mouth_is_dry/my_mouth_is_dry__useruser01__18to39__male__caucasian__20250820T060710.webm: setting an array element with a sequence. The requested array has an inhomogeneous shape after 2 dimensions. The detected shape was (150, 119) + inhomogeneous part.
2025-09-06 13:09:37,378 - lipreading - ERROR - ❌ Error processing icu-videos/18to39/male/caucasian/my_mouth_is_dry/my_mouth_is_dry__useruser01__18to39__male__caucasian__20250820T081545.webm: setting an array element with a sequence. The requested array has an inhomogeneous shape after 2 dimensions. The detected shape was (121, 119) + inhomogeneous part.
2025-09-06 13:09:41,586 - lipreading - ERROR - ❌ Error processing icu-videos/40to64/male/asian/phone/phone__useruser01__40to64__male__asian__20250905T094927.webm: setting an array element with a sequence. The requested array has an inhomogeneous shape after 1 dimensions. The detected shape was (150,) + inhomogeneous part.
2025-09-06 13:09:43,312 - lipreading - ERROR - ❌ Error processing icu-videos/65plus/female/caucasian/phone/phone__useruser01__65plus__female__caucasian__20250827T063232.webm: setting an array element with a sequence. The requested array has an inhomogeneous shape after 1 dimensions. The detected shape was (64,) + inhomogeneous part.
2025-09-06 13:09:44,804 - lipreading - ERROR - ❌ Error processing icu-videos/40to64/female/caucasian/glasses/glasses__useruser01__40to64__female__caucasian__20250827T083305.webm: setting an array element with a sequence. The requested array has an inhomogeneous shape after 1 dimensions. The detected shape was (52,) + inhomogeneous part.
2025-09-06 13:09:46,330 - lipreading - ERROR - ❌ Error processing icu-videos/18to39/female/caucasian/glasses/glasses__useruser01__18to39__female__caucasian__20250804T034830.webm: setting an array element with a sequence. The requested array has an inhomogeneous shape after 2 dimensions. The detected shape was (55, 119) + inhomogeneous part.
2025-09-06 13:09:48,054 - lipreading - ERROR - ❌ Error processing icu-videos/65plus/female/caucasian/i_need_to_move/i_need_to_move__useruser01__65plus__female__caucasian__20250731T063749.webm: setting an array element with a sequence. The requested array has an inhomogeneous shape after 1 dimensions. The detected shape was (61,) + inhomogeneous part.
2025-09-06 13:09:51,660 - lipreading - ERROR - ❌ Error processing icu-videos/18to39/male/caucasian/i_need_to_move/i_need_to_move__useruser01__18to39__male__caucasian__20250820T055740.webm: setting an array element with a sequence. The requested array has an inhomogeneous shape after 1 dimensions. The detected shape was (150,) + inhomogeneous part.
2025-09-06 13:09:54,241 - lipreading - ERROR - ❌ Error processing icu-videos/18to39/male/not_specified/my_back_hurts/my_back_hurts__useruser01__18to39__male__not_specified__20250729T093355.webm: setting an array element with a sequence. The requested array has an inhomogeneous shape after 1 dimensions. The detected shape was (100,) + inhomogeneous part.
2025-09-06 13:09:56,162 - lipreading - ERROR - ❌ Error processing icu-videos/18to39/female/caucasian/my_back_hurts/my_back_hurts__useruser01__18to39__female__caucasian__20250827T084003.webm: setting an array element with a sequence. The requested array has an inhomogeneous shape after 1 dimensions. The detected shape was (72,) + inhomogeneous part.
2025-09-06 13:09:56,164 - lipreading - INFO - ✅ Processing completed. Log saved to: ./data_processed/quality_logs/train_processing_log.csv
2025-09-06 13:10:30,017 - lipreading.s3 - INFO - ✅ Connected to S3 bucket: icudatasetphrasesfortesting
2025-09-06 13:10:30,017 - lipreading - INFO - 🤖 Loading YOLO and SAM models...
2025-09-06 13:10:30,041 - lipreading - WARNING - ⚠️ SAM checkpoint not found: ./checkpoints/sam_vit_b_01ec64.pth
2025-09-06 13:10:30,041 - lipreading - INFO - ✅ Models loaded successfully
2025-09-06 13:10:30,041 - lipreading - INFO - 📋 Processing videos from manifest: manifests/train.csv
2025-09-06 13:10:30,047 - lipreading - INFO - 🔢 Limiting to 2 samples per class
2025-09-06 13:10:30,049 - lipreading - INFO - 📊 Limited dataset size: 20 samples
2025-09-06 13:11:19,295 - lipreading - INFO - ✅ Processing completed. Log saved to: ./data_processed/quality_logs/train_processing_log.csv
2025-09-06 13:11:44,208 - lipreading - INFO - 📊 Setting up data loaders...
2025-09-06 13:11:44,211 - lipreading - INFO - 📋 Loaded manifest with 1625 entries
2025-09-06 13:11:44,213 - lipreading - INFO - 📋 Loaded manifest with 348 entries
2025-09-06 13:11:44,214 - lipreading - INFO - 📋 Loaded manifest with 349 entries
2025-09-06 13:11:44,215 - lipreading - INFO - ✅ Data loaders ready:
2025-09-06 13:11:44,215 - lipreading - INFO -   Train: 50 batches
2025-09-06 13:11:44,215 - lipreading - INFO -   Val:   11 batches
2025-09-06 13:11:44,215 - lipreading - INFO -   Test:  11 batches
2025-09-06 13:11:44,215 - lipreading - INFO - 🤖 Setting up model...
2025-09-06 13:11:44,228 - lipreading - INFO -   Total parameters: 500,138
2025-09-06 13:11:44,228 - lipreading - INFO -   Trainable parameters: 500,138
2025-09-06 13:11:44,228 - lipreading - INFO - ⚙️ Setting up optimizer and scheduler...
2025-09-06 13:12:15,008 - lipreading - INFO - 📊 Setting up data loaders...
2025-09-06 13:12:15,011 - lipreading - INFO - 📋 Loaded manifest with 1625 entries
2025-09-06 13:12:15,013 - lipreading - INFO - 📋 Loaded manifest with 348 entries
2025-09-06 13:12:15,014 - lipreading - INFO - 📋 Loaded manifest with 349 entries
2025-09-06 13:12:15,014 - lipreading - INFO - ✅ Data loaders ready:
2025-09-06 13:12:15,014 - lipreading - INFO -   Train: 50 batches
2025-09-06 13:12:15,014 - lipreading - INFO -   Val:   11 batches
2025-09-06 13:12:15,014 - lipreading - INFO -   Test:  11 batches
2025-09-06 13:12:15,014 - lipreading - INFO - 🤖 Setting up model...
2025-09-06 13:12:15,028 - lipreading - INFO -   Total parameters: 500,138
2025-09-06 13:12:15,028 - lipreading - INFO -   Trainable parameters: 500,138
2025-09-06 13:12:15,028 - lipreading - INFO - ⚙️ Setting up optimizer and scheduler...
2025-09-06 13:12:15,320 - lipreading - INFO -   Optimizer: Adam (lr=0.003)
2025-09-06 13:12:15,320 - lipreading - INFO -   Scheduler: OneCycleLR
2025-09-06 13:12:15,320 - lipreading - INFO - 📊 Using weighted CrossEntropyLoss
2025-09-06 13:12:15,320 - lipreading - INFO - 🚀 Starting training...
2025-09-06 13:14:14,928 - lipreading.s3 - INFO - ✅ Connected to S3 bucket: icudatasetphrasesfortesting
2025-09-06 13:14:14,928 - lipreading - INFO - 🤖 Loading YOLO and SAM models...
2025-09-06 13:14:14,963 - lipreading - WARNING - ⚠️ SAM checkpoint not found: ./checkpoints/sam_vit_b_01ec64.pth
2025-09-06 13:14:14,963 - lipreading - INFO - ✅ Models loaded successfully
2025-09-06 13:14:14,963 - lipreading - INFO - 📋 Processing videos from manifest: manifests/train.csv
2025-09-06 13:14:24,760 - lipreading.s3 - INFO - ✅ Connected to S3 bucket: icudatasetphrasesfortesting
2025-09-06 13:14:24,760 - lipreading - INFO - 🤖 Loading YOLO and SAM models...
2025-09-06 13:14:24,773 - lipreading - WARNING - ⚠️ SAM checkpoint not found: ./checkpoints/sam_vit_b_01ec64.pth
2025-09-06 13:14:24,773 - lipreading - INFO - ✅ Models loaded successfully
2025-09-06 13:14:24,773 - lipreading - INFO - 📋 Processing videos from manifest: manifests/val.csv
2025-09-06 13:33:14,542 - lipreading - INFO - ✅ Processing completed. Log saved to: ./data_processed/quality_logs/val_processing_log.csv
2025-09-06 13:51:00,218 - lipreading.s3 - INFO - ✅ Connected to S3 bucket: icudatasetphrasesfortesting
2025-09-06 13:51:00,218 - lipreading - INFO - 🤖 Loading YOLO and SAM models...
2025-09-06 13:51:00,240 - lipreading - WARNING - ⚠️ SAM checkpoint not found: ./checkpoints/sam_vit_b_01ec64.pth
2025-09-06 13:51:00,240 - lipreading - INFO - ✅ Models loaded successfully
2025-09-06 13:51:00,240 - lipreading - INFO - 📋 Processing videos from manifest: manifests/train.csv
2025-09-06 13:51:00,245 - lipreading - INFO - 🔢 Limiting to 2 samples per class
2025-09-06 13:51:00,248 - lipreading - INFO - 📊 Limited dataset size: 20 samples
2025-09-06 13:56:13,646 - lipreading.s3 - INFO - ✅ Connected to S3 bucket: icudatasetphrasesfortesting
2025-09-06 13:56:13,646 - lipreading - INFO - 🤖 Loading models...
2025-09-06 13:56:13,678 - lipreading - INFO - ✅ YOLO loaded
2025-09-06 13:56:13,678 - lipreading - WARNING - ⚠️ SAM checkpoint not found: ./checkpoints/sam_vit_b_01ec64.pth
2025-09-06 13:56:13,678 - lipreading - WARNING - ⚠️ MediaPipe not available - install with Python 3.11
2025-09-06 13:56:13,678 - lipreading - INFO - ✅ Models loaded successfully
2025-09-06 13:56:13,678 - lipreading - INFO - 📋 Processing videos from manifest: manifests/train.csv
2025-09-06 13:56:13,684 - lipreading - INFO - 🔢 Limiting to 2 samples per class
2025-09-06 13:56:13,687 - lipreading - INFO - 📊 Limited dataset size: 20 samples
2025-09-06 13:56:19,960 - lipreading - INFO - ✅ Processing completed. Log saved to: ./data_processed/quality_logs/train_processing_log.csv
2025-09-06 13:58:28,402 - lipreading.s3 - INFO - ✅ Connected to S3 bucket: icudatasetphrasesfortesting
2025-09-06 13:58:28,402 - lipreading - INFO - 🤖 Loading models...
2025-09-06 13:58:28,419 - lipreading - INFO - ✅ YOLO loaded
2025-09-06 13:58:28,420 - lipreading - WARNING - ⚠️ SAM checkpoint not found: ./checkpoints/sam_vit_b_01ec64.pth
2025-09-06 13:58:28,420 - lipreading - WARNING - ⚠️ MediaPipe not available - install with Python 3.11
2025-09-06 13:58:28,420 - lipreading - INFO - ✅ Models loaded successfully
2025-09-06 13:58:28,420 - lipreading - INFO - 📋 Processing videos from manifest: manifests/train.csv
2025-09-06 13:58:33,967 - lipreading.s3 - INFO - ✅ Connected to S3 bucket: icudatasetphrasesfortesting
2025-09-06 13:58:33,968 - lipreading - INFO - 🤖 Loading models...
2025-09-06 13:58:33,981 - lipreading - INFO - ✅ YOLO loaded
2025-09-06 13:58:33,981 - lipreading - WARNING - ⚠️ SAM checkpoint not found: ./checkpoints/sam_vit_b_01ec64.pth
2025-09-06 13:58:33,981 - lipreading - WARNING - ⚠️ MediaPipe not available - install with Python 3.11
2025-09-06 13:58:33,981 - lipreading - INFO - ✅ Models loaded successfully
2025-09-06 13:58:33,981 - lipreading - INFO - 📋 Processing videos from manifest: manifests/val.csv
2025-09-06 13:58:39,823 - lipreading.s3 - INFO - ✅ Connected to S3 bucket: icudatasetphrasesfortesting
2025-09-06 13:58:39,823 - lipreading - INFO - 🤖 Loading models...
2025-09-06 13:58:39,841 - lipreading - INFO - ✅ YOLO loaded
2025-09-06 13:58:39,841 - lipreading - WARNING - ⚠️ SAM checkpoint not found: ./checkpoints/sam_vit_b_01ec64.pth
2025-09-06 13:58:39,841 - lipreading - WARNING - ⚠️ MediaPipe not available - install with Python 3.11
2025-09-06 13:58:39,841 - lipreading - INFO - ✅ Models loaded successfully
2025-09-06 13:58:39,841 - lipreading - INFO - 📋 Processing videos from manifest: manifests/test.csv
2025-09-06 14:00:23,632 - lipreading - INFO - ✅ Processing completed. Log saved to: ./data_processed/quality_logs/val_processing_log.csv
2025-09-06 14:00:33,458 - lipreading - INFO - ✅ Processing completed. Log saved to: ./data_processed/quality_logs/test_processing_log.csv
2025-09-06 14:06:58,246 - lipreading - INFO - ✅ Processing completed. Log saved to: ./data_processed/quality_logs/train_processing_log.csv
2025-09-06 14:43:43,064 - lipreading.s3 - INFO - ✅ Connected to S3 bucket: icudatasetphrasesfortesting
2025-09-06 14:43:43,066 - lipreading - INFO - 🤖 Loading models...
2025-09-06 14:43:43,100 - lipreading - INFO - ✅ YOLO loaded
2025-09-06 14:43:43,100 - lipreading - WARNING - ⚠️ SAM checkpoint not found: ./checkpoints/sam_vit_b_01ec64.pth
2025-09-06 14:43:43,101 - lipreading - WARNING - ⚠️ MediaPipe not available - install with Python 3.11
2025-09-06 14:43:43,101 - lipreading - INFO - ✅ Models loaded successfully
2025-09-06 14:44:06,894 - lipreading.s3 - INFO - ✅ Connected to S3 bucket: icudatasetphrasesfortesting
2025-09-06 14:44:06,894 - lipreading - INFO - 🤖 Loading models...
2025-09-06 14:44:06,921 - lipreading - INFO - ✅ YOLO loaded
2025-09-06 14:44:06,921 - lipreading - WARNING - ⚠️ SAM checkpoint not found: ./checkpoints/sam_vit_b_01ec64.pth
2025-09-06 14:44:06,921 - lipreading - WARNING - ⚠️ MediaPipe not available - install with Python 3.11
2025-09-06 14:44:06,921 - lipreading - INFO - ✅ Models loaded successfully
2025-09-06 14:45:03,634 - lipreading.s3 - INFO - ✅ Connected to S3 bucket: icudatasetphrasesfortesting
2025-09-06 14:45:03,634 - lipreading - INFO - 🤖 Loading models...
2025-09-06 14:45:03,676 - lipreading - INFO - ✅ YOLO loaded
2025-09-06 14:45:03,676 - lipreading - WARNING - ⚠️ SAM checkpoint not found: ./checkpoints/sam_vit_b_01ec64.pth
2025-09-06 14:45:03,676 - lipreading - WARNING - ⚠️ MediaPipe not available - install with Python 3.11
2025-09-06 14:45:03,676 - lipreading - INFO - ✅ Models loaded successfully
