# ICU Lipreading Pipeline

A production-ready lipreading system for ICU patients, targeting >80% validation accuracy on speaker-held-out data for the 10 most frequent ICU phrases.

## Quick Start

### Prerequisites
- Python 3.13 (main environment)
- Python 3.11 (for MediaPipe if needed: `brew install python@3.11`)
- AWS credentials configured for S3 access (`aws configure`)
- CUDA-capable GPU recommended for training

### Environment Setup
```bash
# Main environment (already active)
source .venv/bin/activate

# Install additional dependencies
pip install -r requirements.txt

# Optional: MediaPipe environment (if needed)
python3.11 -m venv .venv311
source .venv311/bin/activate
pip install mediapipe opencv-python numpy
```

### Quick Test
```bash
# Test that everything is working
python test_pipeline.py

# Run complete pipeline
python run_pipeline.py
```

## Pipeline Steps

### Option 1: Run Complete Pipeline
```bash
python run_pipeline.py --step all
```
Runs all steps from manifest building through training and evaluation.

### Option 2: Run Individual Steps

#### 1. Build Manifests (Speaker-Disjoint Splits)
```bash
python src/manifest_builder.py --config configs/training.yaml
```
Creates train/val/test splits ensuring no speaker appears in multiple sets.

#### 2. Preprocess Data (YOLO→SAM Pipeline)
```bash
python src/preprocess.py --config configs/training.yaml --manifest manifests/train.csv
python src/preprocess.py --config configs/training.yaml --manifest manifests/val.csv
python src/preprocess.py --config configs/training.yaml --manifest manifests/test.csv
```
Downloads from S3, applies YOLO face detection, SAM lip segmentation, and standardizes to 96×96×16 clips.

#### 3. Train Model
```bash
python src/train.py --config configs/training.yaml
```
Trains Mobile3DTiny + BiGRU model with early stopping on validation macro-F1.

#### 4. Evaluate Results
```bash
python src/evaluate.py --config configs/training.yaml --checkpoint checkpoints/best_model.pth
```
Generates comprehensive metrics including confusion matrices and per-class performance.

#### 5. Start Inference Server
```bash
python src/infer_server.py
```
Launches FastAPI server with `/predict` endpoint for iOS app integration.

## API Usage

### Health Check
```bash
curl http://localhost:8000/health
```

### Predict from Video
```bash
curl -X POST "http://localhost:8000/predict" \
  -F "video=@test_video.mp4" \
  -H "Content-Type: multipart/form-data"
```

Response:
```json
{
  "phrase": "help",
  "confidence": 0.89,
  "top5": [
    {"phrase": "help", "confidence": 0.89},
    {"phrase": "doctor", "confidence": 0.07},
    {"phrase": "phone", "confidence": 0.02},
    {"phrase": "pillow", "confidence": 0.01},
    {"phrase": "glasses", "confidence": 0.01}
  ],
  "latency_ms": 245
}
```

## Target Classes
1. pillow (306 samples)
2. phone (259 samples)  
3. doctor (256 samples)
4. glasses (252 samples)
5. i_need_to_move (249 samples)
6. help (247 samples)
7. my_mouth_is_dry (235 samples)
8. my_chest_hurts (178 samples)
9. my_back_hurts (175 samples)
10. i_m_hot (165 samples)

## Architecture

**Pipeline**: Camera → YOLO Face Detection → SAM Lip Segmentation → 96×96×16 Standardization → Mobile3DTiny + BiGRU → Phrase Classification

**Key Features**:
- Speaker-disjoint validation (70/15/15 splits)
- Robust lip ROI extraction with YOLO+SAM
- Temporal standardization (16 frames, 25 FPS)
- Quality filtering (motion, blur, detection confidence)
- Class balancing with weights
- Early stopping on macro-F1

## File Structure
```
├── src/
│   ├── manifest_builder.py    # Create speaker-disjoint splits
│   ├── preprocess.py          # YOLO→SAM preprocessing pipeline  
│   ├── dataset.py             # PyTorch Dataset loader
│   ├── train.py               # Training loop with early stopping
│   ├── evaluate.py            # Comprehensive metrics
│   ├── infer_server.py        # FastAPI prediction server
│   └── utils.py               # S3, logging, and common utilities
├── configs/
│   └── training.yaml          # All configuration parameters
├── manifests/
│   ├── train.csv              # Training split manifest
│   ├── val.csv                # Validation split manifest  
│   └── test.csv               # Test split manifest
├── data_processed/            # Processed 96×96×16 clips
├── checkpoints/               # Model weights and SAM checkpoint
└── logs/                      # Training and processing logs
```

## Success Criteria
- [ ] ≥80% validation accuracy (speaker-held-out)
- [ ] ≥0.80 macro-F1 score
- [ ] No class <0.70 recall
- [ ] <300ms inference latency (GPU) / <1.5s (CPU)
- [ ] >95% clips with valid lip ROI detection

## Troubleshooting

**MediaPipe Issues**: Use Python 3.11 environment for preprocessing if needed.
**CUDA Issues**: Model automatically falls back to CPU if CUDA unavailable.
**S3 Access**: Ensure AWS credentials are configured (`aws configure`).
**Memory Issues**: Reduce batch size in `configs/training.yaml`.

## Development Notes
- All random seeds are fixed for reproducibility
- Quality logs track preprocessing failures
- Class weights handle imbalanced data
- Checkpoints save only the best validation model
- API includes comprehensive error handling
