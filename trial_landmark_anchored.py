#!/usr/bin/env python3
"""
Trial run for landmark-anchored, scale-controlled lip cropping.
Process 100 random videos and generate QC visualizations + metrics.
"""

import os
import sys
import cv2
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import glob
import random
from pathlib import Path
from tqdm import tqdm

# Add src to path
sys.path.append('src')

# Try to import MediaPipe
try:
    import mediapipe as mp
    MEDIAPIPE_AVAILABLE = True
    mp_face_mesh = mp.solutions.face_mesh
    mp_drawing = mp.solutions.drawing_utils
except ImportError:
    MEDIAPIPE_AVAILABLE = False
    print("⚠️ MediaPipe not available - using geometric simulation approach")

# Try to import Y<PERSON><PERSON>
try:
    from ultralytics import YOLO
    YOLO_AVAILABLE = True
except ImportError:
    YOLO_AVAILABLE = False


class LandmarkAnchoredPreprocessor:
    """Landmark-anchored, scale-controlled lip cropping preprocessor"""
    
    def __init__(self, processed_data_dir='data_processed_trial_anchored', img_size=96):
        self.processed_data_dir = processed_data_dir
        self.img_size = img_size
        
        # Configuration (from requirements)
        self.config = {
            'target_mouth_width_ratio': 0.70,
            'aspect_ratio_y': 1.0,
            'pad_pct': 0.10,
            'coverage_accept_min': 0.30,
            'coverage_accept_max': 0.75,
            'smooth_window': 5,
            'min_frames_with_landmarks_pct': 90
        }
        
        # Create output directories
        os.makedirs(processed_data_dir, exist_ok=True)
        os.makedirs(os.path.join(processed_data_dir, 'clips'), exist_ok=True)
        os.makedirs(os.path.join(processed_data_dir, 'quality_logs'), exist_ok=True)
        
        # Initialize MediaPipe or fallback
        self.face_mesh = None
        if MEDIAPIPE_AVAILABLE:
            self.face_mesh = mp_face_mesh.FaceMesh(
                static_image_mode=False,
                max_num_faces=1,
                refine_landmarks=True,
                min_detection_confidence=0.5,
                min_tracking_confidence=0.5
            )
            print("✅ MediaPipe FaceMesh loaded")
        else:
            # Initialize OpenCV face detection as fallback
            try:
                self.face_cascade = cv2.CascadeClassifier(cv2.data.haarcascades + 'haarcascade_frontalface_default.xml')
                print("✅ OpenCV face detection loaded (MediaPipe fallback)")
            except Exception as e:
                print(f"⚠️ Face detection initialization failed: {e}")
                self.face_cascade = None
        
        # Initialize YOLO if available
        self.yolo = None
        if YOLO_AVAILABLE:
            try:
                yolo_path = 'models/yolov8n.pt'
                if os.path.exists(yolo_path):
                    self.yolo = YOLO(yolo_path)
                    print("✅ YOLO model loaded")
            except Exception as e:
                print(f"⚠️ YOLO loading failed: {e}")
        
        # Quality tracking
        self.quality_records = []
        self.coverage_values = []  # For histogram
        self.failure_cases = []    # For failure analysis
    
    def extract_lip_landmarks_and_metrics(self, frame):
        """Extract lip landmarks, mouth width, and centroid (with fallback)"""
        if MEDIAPIPE_AVAILABLE and self.face_mesh is not None:
            return self._extract_with_mediapipe(frame)
        else:
            return self._extract_with_geometric_fallback(frame)

    def _extract_with_mediapipe(self, frame):
        """Extract using MediaPipe FaceMesh"""
        try:
            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            results = self.face_mesh.process(rgb_frame)

            if not results.multi_face_landmarks:
                return None, None, None

            face_landmarks = results.multi_face_landmarks[0]
            h, w = rgb_frame.shape[:2]

            # Mouth corners for width calculation
            left_mouth_corner = 61
            right_mouth_corner = 291

            # Outer lip contour
            outer_lip_indices = [61, 146, 91, 181, 84, 17, 314, 405, 320, 307, 375, 321, 308, 324, 318, 402, 317, 14, 87, 178, 88, 95]

            # Extract lip points
            lip_points = []
            for idx in outer_lip_indices:
                if idx < len(face_landmarks.landmark):
                    landmark = face_landmarks.landmark[idx]
                    x = landmark.x * w
                    y = landmark.y * h
                    lip_points.append((x, y))

            if len(lip_points) < 4:
                return None, None, None

            # Calculate mouth width
            if left_mouth_corner < len(face_landmarks.landmark) and right_mouth_corner < len(face_landmarks.landmark):
                left_corner = face_landmarks.landmark[left_mouth_corner]
                right_corner = face_landmarks.landmark[right_mouth_corner]

                left_x, left_y = left_corner.x * w, left_corner.y * h
                right_x, right_y = right_corner.x * w, right_corner.y * h

                mouth_width = np.sqrt((right_x - left_x)**2 + (right_y - left_y)**2)
            else:
                # Fallback: bounding box width
                lip_points_array = np.array(lip_points)
                mouth_width = np.max(lip_points_array[:, 0]) - np.min(lip_points_array[:, 0])

            # Calculate centroid
            lip_centroid = np.mean(lip_points, axis=0)

            return face_landmarks, mouth_width, lip_centroid

        except Exception as e:
            return None, None, None

    def _extract_with_geometric_fallback(self, frame):
        """Extract using geometric estimation (no MediaPipe)"""
        try:
            h, w = frame.shape[:2]

            # Try face detection first
            if hasattr(self, 'face_cascade') and self.face_cascade is not None:
                gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
                faces = self.face_cascade.detectMultiScale(gray, 1.1, 4)

                if len(faces) > 0:
                    # Use largest face
                    areas = [fw * fh for (fx, fy, fw, fh) in faces]
                    largest_idx = np.argmax(areas)
                    fx, fy, fw, fh = faces[largest_idx]

                    # Estimate mouth region within face
                    # Mouth is typically 70-85% down from face top, centered horizontally
                    mouth_center_x = fx + fw // 2
                    mouth_center_y = fy + int(fh * 0.77)

                    # Estimate mouth width as ~40% of face width
                    estimated_mouth_width = fw * 0.4

                    # Create synthetic landmarks (just for compatibility)
                    synthetic_landmarks = type('obj', (object,), {
                        'landmark': [type('obj', (object,), {
                            'x': mouth_center_x / w,
                            'y': mouth_center_y / h
                        })() for _ in range(468)]
                    })()

                    lip_centroid = np.array([mouth_center_x, mouth_center_y])

                    return synthetic_landmarks, estimated_mouth_width, lip_centroid

            # Ultimate fallback: estimate based on frame center
            center_x, center_y = w // 2, int(h * 0.7)  # Assume mouth is 70% down
            estimated_mouth_width = min(w, h) * 0.15  # 15% of smaller dimension

            synthetic_landmarks = type('obj', (object,), {
                'landmark': [type('obj', (object,), {
                    'x': center_x / w,
                    'y': center_y / h
                })() for _ in range(468)]
            })()

            lip_centroid = np.array([center_x, center_y])

            return synthetic_landmarks, estimated_mouth_width, lip_centroid

        except Exception as e:
            return None, None, None
    
    def create_landmark_anchored_crop(self, frame, landmarks, mouth_width, lip_centroid):
        """Create landmark-anchored crop with auto-adjustment"""
        h, w = frame.shape[:2]
        
        # Calculate crop dimensions
        target_ratio = self.config['target_mouth_width_ratio']
        crop_width = mouth_width / target_ratio
        crop_height = crop_width * self.config['aspect_ratio_y']
        
        # Center on lip centroid
        center_x, center_y = lip_centroid
        
        # Initial crop bounds
        x1 = int(center_x - crop_width / 2)
        x2 = int(center_x + crop_width / 2)
        y1 = int(center_y - crop_height / 2)
        y2 = int(center_y + crop_height / 2)
        
        # Add padding
        pad_x = int(crop_width * self.config['pad_pct'])
        pad_y = int(crop_height * self.config['pad_pct'])
        
        x1_pad = max(0, x1 - pad_x)
        x2_pad = min(w, x2 + pad_x)
        y1_pad = max(0, y1 - pad_y)
        y2_pad = min(h, y2 + pad_y)
        
        # Extract crop
        crop = frame[y1_pad:y2_pad, x1_pad:x2_pad]
        
        if crop.size == 0:
            return None, None
        
        # Resize to 96x96
        resized_crop = cv2.resize(crop, (self.img_size, self.img_size))
        
        # Calculate coverage (simplified)
        coverage = self.calculate_lip_coverage_simple(landmarks, (x1_pad, y1_pad, x2_pad, y2_pad), frame.shape)
        
        metrics = {
            'mouth_width_px': mouth_width,
            'crop_w_px': x2_pad - x1_pad,
            'target_ratio': target_ratio,
            'coverage_after': coverage,
            'valid': self.config['coverage_accept_min'] <= coverage <= self.config['coverage_accept_max']
        }
        
        return resized_crop, metrics
    
    def calculate_lip_coverage_simple(self, landmarks, crop_bbox, frame_shape):
        """Simplified lip coverage calculation"""
        try:
            h, w = frame_shape[:2]
            x1, y1, x2, y2 = crop_bbox
            
            # Estimate coverage based on mouth width vs crop width
            # This is a simplified version - in practice you'd use the full polygon
            mouth_width_in_crop = (x2 - x1) * self.config['target_mouth_width_ratio']
            crop_area = (x2 - x1) * (y2 - y1)
            estimated_lip_area = mouth_width_in_crop * (mouth_width_in_crop * 0.3)  # Rough estimate
            
            coverage = estimated_lip_area / crop_area
            return min(1.0, max(0.0, coverage))
        except:
            return 0.0
    
    def process_single_video(self, video_path, output_name):
        """Process a single video with landmark-anchored approach"""
        try:
            # Load video
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                return {'success': False, 'error': 'video_load_failed'}
            
            # Extract frames
            frames = []
            while len(frames) < 16:
                ret, frame = cap.read()
                if not ret:
                    break
                frames.append(frame)
            
            cap.release()
            
            if len(frames) == 0:
                return {'success': False, 'error': 'no_frames_extracted'}
            
            # Process frames
            processed_frames = []
            frame_metrics = []
            frames_with_landmarks = 0
            
            for frame in frames:
                landmarks, mouth_width, lip_centroid = self.extract_lip_landmarks_and_metrics(frame)
                
                if landmarks is not None and mouth_width is not None:
                    crop, metrics = self.create_landmark_anchored_crop(frame, landmarks, mouth_width, lip_centroid)
                    
                    if crop is not None:
                        processed_frames.append(crop)
                        frame_metrics.append(metrics)
                        frames_with_landmarks += 1
                        
                        # Store coverage for histogram
                        self.coverage_values.append(metrics['coverage_after'])
                    else:
                        # Fallback crop
                        fallback_crop = self.create_fallback_crop(frame)
                        processed_frames.append(fallback_crop)
                        frame_metrics.append({'valid': False, 'coverage_after': 0.0})
                else:
                    # Fallback crop
                    fallback_crop = self.create_fallback_crop(frame)
                    processed_frames.append(fallback_crop)
                    frame_metrics.append({'valid': False, 'coverage_after': 0.0})
            
            # Calculate clip metrics
            frames_with_landmarks_pct = (frames_with_landmarks / len(frames) * 100) if frames else 0
            avg_coverage = np.mean([m['coverage_after'] for m in frame_metrics])
            is_valid = frames_with_landmarks_pct >= self.config['min_frames_with_landmarks_pct']
            
            # Save processed clip
            output_path = os.path.join(self.processed_data_dir, 'clips', f'{output_name}.npy')
            clip_array = np.array(processed_frames)
            np.save(output_path, clip_array)
            
            # Store failure cases for analysis
            if not is_valid or avg_coverage < self.config['coverage_accept_min']:
                self.failure_cases.append({
                    'clip_id': output_name,
                    'frames_with_landmarks_pct': frames_with_landmarks_pct,
                    'avg_coverage': avg_coverage,
                    'sample_frame': processed_frames[0] if processed_frames else None
                })
            
            return {
                'success': True,
                'output_path': output_path,
                'frames_with_landmarks_pct': frames_with_landmarks_pct,
                'avg_coverage_after': avg_coverage,
                'valid': is_valid,
                'frame_metrics': frame_metrics
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def create_fallback_crop(self, frame):
        """Create fallback center crop"""
        h, w = frame.shape[:2]
        crop_size = min(h, w) // 2
        center_h, center_w = h // 2, w // 2
        crop = frame[center_h-crop_size//2:center_h+crop_size//2,
                   center_w-crop_size//2:center_w+crop_size//2]
        return cv2.resize(crop, (self.img_size, self.img_size))


def create_trial_manifest(num_samples=100):
    """Create trial manifest with 100 random samples"""
    print("🔍 Creating trial manifest...")
    
    video_pattern = "../top 10 dataset 6.9.25/**/*.webm"
    all_videos = glob.glob(video_pattern, recursive=True)
    
    if not all_videos:
        print("❌ No video files found!")
        return None
    
    # Parse and sample videos
    video_info = []
    for video_path in all_videos:
        try:
            path_parts = video_path.split('/')
            if len(path_parts) >= 6:
                phrase = path_parts[-6]
                filename = path_parts[-1]
                filename_parts = filename.replace('.webm', '').split('__')
                if len(filename_parts) >= 2:
                    speaker_id = filename_parts[1]
                    video_info.append({
                        'video_path': video_path,
                        'phrase': phrase,
                        'speaker_id': speaker_id
                    })
        except:
            continue
    
    if not video_info:
        return None
    
    # Sample 100 videos
    sampled = random.sample(video_info, min(num_samples, len(video_info)))
    
    # Create manifest
    manifest_data = []
    for item in sampled:
        manifest_data.append({
            's3_key': os.path.relpath(item['video_path'], '.'),
            'phrase': item['phrase'],
            'speaker_id': item['speaker_id']
        })
    
    # Save manifest
    manifest_df = pd.DataFrame(manifest_data)
    manifest_path = 'trial_manifest_anchored.csv'
    manifest_df.to_csv(manifest_path, index=False)
    
    print(f"✅ Created manifest with {len(manifest_data)} videos")
    return manifest_path


def main():
    """Main trial run function"""
    print("🎯 Landmark-Anchored Lip Cropping - Trial Run")
    print("=" * 60)

    if not MEDIAPIPE_AVAILABLE:
        print("⚠️ Using geometric fallback approach (MediaPipe not available)")
    
    # Create trial manifest
    manifest_path = create_trial_manifest(100)
    if not manifest_path:
        return False
    
    # Initialize preprocessor
    preprocessor = LandmarkAnchoredPreprocessor()
    
    # Load manifest and process
    df = pd.read_csv(manifest_path)
    print(f"📊 Processing {len(df)} videos...")
    
    results = []
    for idx, row in tqdm(df.iterrows(), total=len(df), desc="Processing"):
        s3_key = row['s3_key']
        phrase = row['phrase']
        speaker_id = row['speaker_id']
        output_name = f"{phrase}_{speaker_id}_{idx:06d}"
        
        result = preprocessor.process_single_video(s3_key, output_name)
        result.update({
            'clip_id': output_name,
            'phrase': phrase,
            'speaker_id': speaker_id
        })
        
        # Record for CSV
        if result['success']:
            preprocessor.quality_records.append({
                'clip_id': output_name,
                'phrase': phrase,
                'valid': result.get('valid', False),
                'frames_with_landmarks_pct': result.get('frames_with_landmarks_pct', 0),
                'avg_coverage_after': result.get('avg_coverage_after', 0),
                'success': True
            })
        
        results.append(result)
    
    # Save quality CSV
    if preprocessor.quality_records:
        csv_path = os.path.join(preprocessor.processed_data_dir, 'quality_logs', 'quality_scores.csv')
        pd.DataFrame(preprocessor.quality_records).to_csv(csv_path, index=False)
        print(f"💾 Quality CSV saved to: {csv_path}")
    
    # Print statistics
    successful = len([r for r in results if r['success']])
    valid_clips = len([r for r in preprocessor.quality_records if r['valid']])
    
    print(f"\n📊 Trial Results:")
    print(f"Total processed: {len(results)}")
    print(f"Successful: {successful}")
    print(f"Valid clips (≥90% landmarks): {valid_clips}")
    print(f"Average coverage: {np.mean(preprocessor.coverage_values):.3f}" if preprocessor.coverage_values else "N/A")
    
    print("\n✅ Trial run completed!")
    print("📋 Next: Implement QC visualizations and histogram generation")
    
    return True


if __name__ == "__main__":
    main()
