"""
Preprocessing Pipeline for ICU Lipreading
YOLO Face Detection → SAM Lip Segmentation → Standardization
"""

import os
import cv2
import numpy as np
import pandas as pd
import tempfile
from pathlib import Path
from typing import List, Dict, Tuple, Optional
import argparse
from tqdm import tqdm
import torch
from ultralytics import YOLO
from segment_anything import SamPredictor, sam_model_registry

from utils import (
    setup_logging, set_seed, load_config, S3Manager,
    create_directories, get_video_info, calculate_motion_score,
    calculate_blur_score, save_frames_as_npy
)


class VideoPreprocessor:
    """Preprocess videos using YOLO→SAM pipeline"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.logger = setup_logging(
            config.get('logging', {}).get('level', 'INFO'),
            config.get('logging', {}).get('log_dir', './logs')
        )
        
        # Configuration
        self.img_size = config['data']['img_size']
        self.frame_length = config['data']['frames_per_clip']
        self.fps_target = config['data']['fps_target']
        self.processed_data_dir = config['data']['data_processed_dir']
        
        # Quality thresholds
        self.min_face_area = config['segmentation']['quality']['min_face_area']
        self.min_motion_threshold = config['segmentation']['quality']['min_motion_threshold']
        self.max_blur_threshold = config['segmentation']['quality']['max_blur_threshold']
        
        # Initialize S3 manager
        self.s3_manager = S3Manager(config['data']['s3_bucket'])
        
        # Create output directories
        create_directories([
            self.processed_data_dir,
            os.path.join(self.processed_data_dir, 'clips'),
            os.path.join(self.processed_data_dir, 'quality_logs')
        ])
        
        # Initialize models
        self._load_models()

        # Quality tracking
        self.quality_stats = {
            'total_processed': 0,
            'successful': 0,
            'invalid_crop': 0,
            'no_landmarks': 0,
            'low_confidence': 0,
            'processing_error': 0
        }
    
    def _load_models(self):
        """Load YOLO, SAM, and MediaPipe models"""
        self.logger.info("🤖 Loading models...")

        # Load YOLO (optional for quality assessment)
        try:
            yolo_model = self.config['segmentation']['yolo']['model']
            self.yolo = YOLO(yolo_model)
            self.yolo_conf = self.config['segmentation']['yolo']['conf_threshold']
            self.logger.info("✅ YOLO loaded")
        except Exception as e:
            self.logger.warning(f"⚠️ YOLO loading failed: {e}")
            self.yolo = None

        # Load SAM (optional, skip if PyTorch 2.6 compatibility issues)
        sam_type = self.config['segmentation']['sam']['model_type']
        sam_checkpoint = self.config['segmentation']['sam']['checkpoint_path']

        if not os.path.exists(sam_checkpoint):
            self.logger.warning(f"⚠️ SAM checkpoint not found: {sam_checkpoint}")
            self.sam_predictor = None
        else:
            try:
                device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
                from utils import load_sam_model
                self.sam_predictor = load_sam_model(sam_type, sam_checkpoint, device)
                self.logger.info("✅ SAM model loaded")
            except Exception as e:
                self.logger.warning(f"⚠️ SAM loading failed: {e}")
                self.sam_predictor = None

        # Load MediaPipe FaceMesh
        try:
            import mediapipe as mp
            self.mp_face_mesh = mp.solutions.face_mesh
            self.face_mesh = self.mp_face_mesh.FaceMesh(
                static_image_mode=True,
                max_num_faces=1,
                refine_landmarks=True,
                min_detection_confidence=0.5,
                min_tracking_confidence=0.5
            )
            self.logger.info("✅ MediaPipe FaceMesh loaded")
            self.mediapipe_available = True
        except ImportError:
            self.logger.warning("⚠️ MediaPipe not available - install with Python 3.11")
            self.face_mesh = None
            self.mediapipe_available = False
        except Exception as e:
            self.logger.warning(f"⚠️ MediaPipe loading failed: {e}")
            self.face_mesh = None
            self.mediapipe_available = False

        self.logger.info("✅ Models loaded successfully")

        # Initialize quality tracking CSV
        self.quality_records = []
    
    def extract_frames_from_video(self, video_path: str) -> Optional[np.ndarray]:
        """Extract frames from video file"""
        cap = cv2.VideoCapture(video_path)

        if not cap.isOpened():
            self.logger.error(f"❌ Cannot open video: {video_path}")
            return None

        frames = []
        frame_count = 0
        max_frames = 200  # Limit to prevent memory issues

        while frame_count < max_frames:
            ret, frame = cap.read()
            if not ret:
                break

            # Ensure frame has consistent shape
            if frame is None or frame.size == 0:
                continue

            # Convert BGR to RGB
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

            # Ensure all frames have the same shape
            if len(frames) > 0 and frame_rgb.shape != frames[0].shape:
                # Resize to match first frame
                frame_rgb = cv2.resize(frame_rgb, (frames[0].shape[1], frames[0].shape[0]))

            frames.append(frame_rgb)
            frame_count += 1

        cap.release()

        if not frames:
            return None

        try:
            return np.array(frames)
        except ValueError as e:
            self.logger.error(f"❌ Error creating frame array: {e}")
            return None
    
    def extract_landmark_driven_lip_crop(self, frame: np.ndarray) -> tuple:
        """
        Extract lip crop using landmark-driven approach (adapted for Python 3.13 without MediaPipe):
        1. Use YOLO face detection to find face
        2. Apply geometric heuristics to locate lip region within face
        3. Create tight bounding box around estimated lip region + 20% padding
        4. Validate using face detection confidence and geometric constraints
        Returns (crop, is_valid, has_landmarks, confidence, adjustment_info, face_info)
        """
        h, w = frame.shape[:2]

        # Step 1: YOLO face detection for precise face localization
        face_bbox = None
        face_confidence = 0.0

        if self.yolo is not None:
            try:
                results = self.yolo(frame, conf=self.yolo_conf, classes=[0], verbose=False)
                if results and results[0].boxes:
                    boxes = results[0].boxes.xyxy.cpu().numpy()
                    confidences = results[0].boxes.conf.cpu().numpy()

                    if len(boxes) > 0:
                        # Get the most confident face detection
                        best_idx = np.argmax(confidences)
                        box = boxes[best_idx]
                        face_confidence = confidences[best_idx]
                        x1, y1, x2, y2 = map(int, box)
                        face_bbox = (x1, y1, x2, y2)
            except Exception as e:
                self.logger.debug(f"YOLO detection failed: {e}")

        if face_bbox is not None and face_confidence > self.yolo_conf:
            # Step 2: Apply geometric heuristics to locate lip region within face
            fx1, fy1, fx2, fy2 = face_bbox
            face_w = fx2 - fx1
            face_h = fy2 - fy1

            # Geometric lip region estimation based on facial proportions
            # Lips are typically located in the lower third of the face
            # Horizontally centered, occupying about 40% of face width

            # Vertical positioning: lips are roughly 70-85% down from top of face
            lip_center_y = fy1 + int(face_h * 0.77)  # 77% down from face top
            lip_height = max(int(face_h * 0.15), 24)  # 15% of face height, min 24px

            # Horizontal positioning: centered, 40% of face width
            lip_center_x = fx1 + face_w // 2
            lip_width = max(int(face_w * 0.4), 32)  # 40% of face width, min 32px

            # Create tight bounding box around estimated lip region
            lip_x1 = lip_center_x - lip_width // 2
            lip_x2 = lip_center_x + lip_width // 2
            lip_y1 = lip_center_y - lip_height // 2
            lip_y2 = lip_center_y + lip_height // 2

            # Ensure lip region is within face bounds
            lip_x1 = max(fx1, lip_x1)
            lip_x2 = min(fx2, lip_x2)
            lip_y1 = max(fy1, lip_y1)
            lip_y2 = min(fy2, lip_y2)

            # Add 20% padding around the estimated lip region
            lip_w = lip_x2 - lip_x1
            lip_h = lip_y2 - lip_y1
            pad_x = int(lip_w * 0.2)
            pad_y = int(lip_h * 0.2)

            # Apply padding with frame bounds checking
            x1_pad = max(0, lip_x1 - pad_x)
            x2_pad = min(w, lip_x2 + pad_x)
            y1_pad = max(0, lip_y1 - pad_y)
            y2_pad = min(h, lip_y2 + pad_y)

            # Extract crop
            lip_crop = frame[y1_pad:y2_pad, x1_pad:x2_pad]

            if lip_crop.size > 0:
                resized_crop = cv2.resize(lip_crop, (self.img_size, self.img_size))

                # Validate crop quality based on face detection confidence and geometric constraints
                is_valid, confidence = self._validate_geometric_lip_crop(
                    face_bbox, (x1_pad, y1_pad, x2_pad, y2_pad), face_confidence
                )

                face_info = {
                    'face_bbox': face_bbox,
                    'lip_bbox': (lip_x1, lip_y1, lip_x2, lip_y2),
                    'padded_bbox': (x1_pad, y1_pad, x2_pad, y2_pad),
                    'face_confidence': face_confidence
                }

                return resized_crop, is_valid, True, confidence, "geometric_landmark_driven", face_info

        # Step 3: Fallback to grid-based approach if face detection fails
        self.logger.debug("Face detection failed, falling back to grid approach")

        # Apply 3x2 grid heuristic on full frame
        col_width = w // 3
        row_height = h // 2

        # Top-middle cell (most likely to contain lips in typical video framing)
        x1 = col_width
        x2 = 2 * col_width
        y1 = 0
        y2 = row_height

        # Ensure coordinates are within bounds
        x1 = max(0, min(x1, w-1))
        x2 = max(x1+1, min(x2, w))
        y1 = max(0, min(y1, h-1))
        y2 = max(y1+1, min(y2, h))

        # Extract fallback crop
        fallback_crop = frame[y1:y2, x1:x2]

        if fallback_crop.size > 0:
            resized_crop = cv2.resize(fallback_crop, (self.img_size, self.img_size))

            # Lower confidence for fallback approach
            is_valid = False  # Mark as invalid since we couldn't detect face
            confidence = 0.3  # Low confidence for grid fallback
            has_landmarks = False
            adjustment_info = "grid_fallback_no_face"

            face_info = {
                'face_bbox': None,
                'lip_bbox': None,
                'padded_bbox': (x1, y1, x2, y2),
                'face_confidence': 0.0
            }

            return resized_crop, is_valid, has_landmarks, confidence, adjustment_info, face_info
        else:
            # Final fallback: center crop
            crop_size = min(h, w)
            start_h = (h - crop_size) // 2
            start_w = (w - crop_size) // 2
            center_crop = frame[start_h:start_h+crop_size, start_w:start_w+crop_size]
            resized_crop = cv2.resize(center_crop, (self.img_size, self.img_size))

            face_info = {
                'face_bbox': None,
                'lip_bbox': None,
                'padded_bbox': (start_w, start_h, start_w+crop_size, start_h+crop_size),
                'face_confidence': 0.0
            }

            return resized_crop, False, False, 0.0, "center_crop_fallback", face_info

    def _validate_geometric_lip_crop(self, face_bbox, crop_bbox, face_confidence) -> tuple:
        """
        Validate geometric lip crop based on face detection confidence and geometric constraints.
        Returns (is_valid, confidence)
        """
        try:
            if face_bbox is None:
                return False, 0.0

            fx1, fy1, fx2, fy2 = face_bbox
            cx1, cy1, cx2, cy2 = crop_bbox

            face_w = fx2 - fx1
            face_h = fy2 - fy1
            crop_w = cx2 - cx1
            crop_h = cy2 - cy1

            # Base confidence from face detection
            confidence = face_confidence

            # Geometric validation checks

            # 1. Check if crop is reasonably positioned within face
            crop_center_x = (cx1 + cx2) // 2
            crop_center_y = (cy1 + cy2) // 2
            face_center_x = (fx1 + fx2) // 2
            face_center_y = (fy1 + fy2) // 2

            # Crop should be in lower half of face (lips are in lower face)
            if crop_center_y < face_center_y:
                confidence *= 0.7  # Penalty for crop in upper face

            # Crop should be horizontally centered relative to face
            horizontal_offset = abs(crop_center_x - face_center_x) / face_w
            if horizontal_offset > 0.3:  # More than 30% offset from face center
                confidence *= (1.0 - horizontal_offset)

            # 2. Check crop size relative to face size
            crop_to_face_ratio_w = crop_w / face_w
            crop_to_face_ratio_h = crop_h / face_h

            # Ideal lip crop should be 30-60% of face width and 10-25% of face height
            if not (0.2 <= crop_to_face_ratio_w <= 0.8):
                confidence *= 0.8  # Penalty for unusual width ratio

            if not (0.08 <= crop_to_face_ratio_h <= 0.4):
                confidence *= 0.8  # Penalty for unusual height ratio

            # 3. Check minimum crop size (avoid too small crops)
            min_crop_size = 24
            if crop_w < min_crop_size or crop_h < min_crop_size:
                confidence *= 0.5  # Heavy penalty for too small crops

            # 4. Face detection confidence threshold
            if face_confidence < 0.5:
                confidence *= 0.6  # Penalty for low face detection confidence

            # Consider valid if confidence >= 90% (high threshold for landmark-driven approach)
            is_valid = confidence >= 0.9

            return is_valid, confidence

        except Exception as e:
            self.logger.debug(f"Geometric validation failed: {e}")
            return False, 0.0
    
    def segment_lips_with_sam(self, frame: np.ndarray, face_bbox: Tuple[int, int, int, int]) -> Optional[np.ndarray]:
        """Segment lips using SAM within face bounding box"""
        if self.sam_predictor is None:
            return None

        x1, y1, x2, y2 = face_bbox

        # Extract face region
        face_crop = frame[y1:y2, x1:x2]

        if face_crop.size == 0:
            return None

        try:
            # Set image for SAM
            self.sam_predictor.set_image(face_crop)

            # Create prompt point in lower center of face (approximate lip location)
            face_h, face_w = face_crop.shape[:2]
            lip_point = np.array([[face_w // 2, int(face_h * 0.75)]])  # Lower center
            lip_label = np.array([1])

            # Generate mask
            masks, scores, _ = self.sam_predictor.predict(
                point_coords=lip_point,
                point_labels=lip_label,
                multimask_output=True
            )

            if len(masks) == 0:
                return None

            # Select best mask
            best_mask = masks[np.argmax(scores)]

            # Apply mask to face crop
            masked_face = face_crop.copy()
            masked_face[~best_mask] = 0  # Set non-lip regions to black

            return masked_face

        except Exception as e:
            self.logger.debug(f"SAM segmentation failed: {e}")
            return None
    
    def standardize_clip(self, frames: np.ndarray) -> Optional[np.ndarray]:
        """Standardize clip to fixed size and frame count"""
        if len(frames) == 0:
            return None
        
        # Temporal standardization: sample to target frame count
        current_frames = len(frames)
        
        if current_frames < self.frame_length:
            # Pad by repeating last frame
            padding_needed = self.frame_length - current_frames
            last_frame = frames[-1:].repeat(padding_needed, axis=0)
            frames = np.concatenate([frames, last_frame], axis=0)
        elif current_frames > self.frame_length:
            # Uniform sampling
            indices = np.linspace(0, current_frames - 1, self.frame_length, dtype=int)
            frames = frames[indices]
        
        # Spatial standardization: resize to target size
        standardized_frames = []
        for frame in frames:
            if frame.shape[:2] != (self.img_size, self.img_size):
                resized = cv2.resize(frame, (self.img_size, self.img_size))
                standardized_frames.append(resized)
            else:
                standardized_frames.append(frame)
        
        standardized_frames = np.array(standardized_frames)
        
        # Pixel normalization: scale to [0, 1]
        standardized_frames = standardized_frames.astype(np.float32) / 255.0
        
        return standardized_frames
    


    def process_single_video(self, s3_key: str, output_name: str) -> Dict[str, any]:
        """Process a single video through the complete pipeline"""
        self.quality_stats['total_processed'] += 1

        # Download video to temporary file
        with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as tmp_file:
            temp_path = tmp_file.name

        try:
            # Download from S3
            if not self.s3_manager.download_video(s3_key, temp_path):
                self.quality_stats['processing_error'] += 1
                return {'success': False, 'error': 'download_failed'}

            # Extract frames
            frames = self.extract_frames_from_video(temp_path)
            if frames is None:
                self.quality_stats['processing_error'] += 1
                return {'success': False, 'error': 'frame_extraction_failed'}

            # Process each frame using landmark-driven cropping strategy
            processed_frames = []
            validation_results = []
            all_face_info = []

            for frame in frames:
                # Apply landmark-driven lip crop: Geometric approach with YOLO face detection
                lip_crop, is_valid, has_landmarks, confidence, adjustment_info, face_info = self.extract_landmark_driven_lip_crop(frame)

                validation_results.append({
                    'valid': is_valid,
                    'confidence': confidence,
                    'has_landmarks': has_landmarks,
                    'adjustment': adjustment_info
                })

                processed_frames.append(lip_crop)
                all_face_info.append(face_info)

            if not processed_frames:
                self.quality_stats['processing_error'] += 1
                return {'success': False, 'error': 'no_frames_processed'}

            # Convert to numpy array - all frames should now have the same shape
            try:
                processed_frames = np.array(processed_frames)
            except ValueError as e:
                self.quality_stats['processing_error'] += 1
                return {'success': False, 'error': f'frame_array_creation_failed: {e}'}

            # Auto-scoring system: Calculate landmark coverage percentage with 90% threshold
            frames_with_landmarks = sum(1 for r in validation_results if r['has_landmarks'])
            valid_frames = sum(1 for r in validation_results if r['valid'])
            total_frames = len(validation_results)

            # Calculate landmark coverage percentage (frames where lip landmarks are fully inside ROI)
            landmark_coverage_pct = (valid_frames / total_frames * 100) if total_frames > 0 else 0
            frames_with_landmarks_pct = (frames_with_landmarks / total_frames * 100) if total_frames > 0 else 0
            avg_confidence = np.mean([r['confidence'] for r in validation_results]) if validation_results else 0

            # Apply 90% threshold for training acceptance (landmark-driven approach)
            is_high_quality = landmark_coverage_pct >= 90.0

            if not is_high_quality:
                # Mark as low quality but still process (for logging)
                if landmark_coverage_pct < 90.0:
                    self.quality_stats['invalid_crop'] += 1
                    error_reason = 'low_landmark_coverage'
                else:
                    self.quality_stats['invalid_crop'] += 1
                    error_reason = 'quality_threshold_not_met'

                # Still process the clip but mark as low quality
                quality_info = {
                    'success': False,
                    'error': error_reason,
                    'landmark_coverage_pct': landmark_coverage_pct,
                    'frames_with_landmarks_pct': frames_with_landmarks_pct,
                    'avg_confidence': avg_confidence,
                    'valid_frames': valid_frames,
                    'total_frames': total_frames,
                    'is_high_quality': False
                }
            else:
                # High quality clip (≥90% landmark coverage)
                quality_info = {
                    'success': True,
                    'landmark_coverage_pct': landmark_coverage_pct,
                    'frames_with_landmarks_pct': frames_with_landmarks_pct,
                    'avg_confidence': avg_confidence,
                    'valid_frames': valid_frames,
                    'total_frames': total_frames,
                    'is_high_quality': True
                }

            # Standardize clip
            standardized_clip = self.standardize_clip(processed_frames)
            if standardized_clip is None:
                self.quality_stats['processing_error'] += 1
                return {'success': False, 'error': 'standardization_failed'}

            # Save processed clip
            output_path = os.path.join(self.processed_data_dir, 'clips', f"{output_name}.npy")
            save_frames_as_npy(standardized_clip, output_path)

            self.quality_stats['successful'] += 1

            # Update quality info with processing details
            quality_info.update({
                'output_path': output_path,
                'original_frames': len(frames),
                'processed_frames': len(standardized_clip),
                'has_landmarks': frames_with_landmarks > 0
            })

            return quality_info

        except Exception as e:
            self.logger.error(f"❌ Error processing {s3_key}: {e}")
            self.quality_stats['processing_error'] += 1
            return {'success': False, 'error': str(e)}

        finally:
            # Clean up temporary file
            if os.path.exists(temp_path):
                os.unlink(temp_path)

    def process_from_manifest(self, manifest_path: str, limit_per_class: Optional[int] = None):
        """Process all videos listed in a manifest file"""
        self.logger.info(f"📋 Processing videos from manifest: {manifest_path}")

        # Load manifest
        df = pd.read_csv(manifest_path)

        # Apply limit per class if specified
        if limit_per_class is not None:
            self.logger.info(f"🔢 Limiting to {limit_per_class} samples per class")
            limited_dfs = []
            for phrase in df['phrase'].unique():
                phrase_df = df[df['phrase'] == phrase].head(limit_per_class)
                limited_dfs.append(phrase_df)
            df = pd.concat(limited_dfs, ignore_index=True)
            self.logger.info(f"📊 Limited dataset size: {len(df)} samples")

        # Process each video
        results = []

        for idx, row in tqdm(df.iterrows(), total=len(df), desc="Processing videos"):
            s3_key = row['s3_key']
            phrase = row['phrase']
            speaker_id = row['speaker_id']

            # Generate output name
            output_name = f"{phrase}_{speaker_id}_{idx:06d}"

            # Process video
            result = self.process_single_video(s3_key, output_name)

            # Add metadata
            result.update({
                'manifest_idx': idx,
                's3_key': s3_key,
                'phrase': phrase,
                'speaker_id': speaker_id,
                'output_name': output_name
            })

            # Record quality data for CSV (landmark-driven approach)
            if 'landmark_coverage_pct' in result:
                quality_record = {
                    'clip_id': output_name,
                    'phrase': phrase,
                    'speaker_id': speaker_id,
                    'valid_crop': result.get('is_high_quality', False),
                    'landmark_coverage_pct': result.get('landmark_coverage_pct', 0),
                    'frames_with_landmarks_pct': result.get('frames_with_landmarks_pct', 0),
                    'avg_confidence': result.get('avg_confidence', 0),
                    'valid_frames': result.get('valid_frames', 0),
                    'total_frames': result.get('total_frames', 0),
                    'success': result.get('success', False),
                    'error': result.get('error', '')
                }
                self.quality_records.append(quality_record)

            results.append(result)

        # Save processing log
        log_name = os.path.basename(manifest_path).replace('.csv', '_processing_log.csv')
        log_path = os.path.join(self.processed_data_dir, 'quality_logs', log_name)

        results_df = pd.DataFrame(results)
        results_df.to_csv(log_path, index=False)

        # Save quality CSV with auto-scoring results
        quality_csv_path = os.path.join(self.processed_data_dir, 'quality_logs', 'quality_scores.csv')
        self._save_quality_csv(quality_csv_path)

        # Print statistics
        self._print_processing_stats()

        self.logger.info(f"✅ Processing completed. Log saved to: {log_path}")
        self.logger.info(f"📊 Quality scores saved to: {quality_csv_path}")

    def _save_quality_csv(self, output_path="data_processed/quality_logs/quality_scores.csv"):
        """Save quality records to CSV"""
        if not self.quality_records:
            return

        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        import pandas as pd
        df = pd.DataFrame(self.quality_records)
        df.to_csv(output_path, index=False)

        self.logger.info(f"💾 Quality scores saved to: {output_path}")

        # Print summary statistics
        high_quality_count = len(df[df['valid_crop'] == True])
        total_count = len(df)
        avg_landmark_coverage = df['landmark_coverage_pct'].mean()

        print(f"\n📊 Quality Summary:")
        print("=" * 50)
        print(f"Total clips processed: {total_count}")
        print(f"High quality (≥80%):   {high_quality_count} ({high_quality_count/total_count*100:.1f}%)")
        print(f"Low quality (<80%):    {total_count-high_quality_count} ({(total_count-high_quality_count)/total_count*100:.1f}%)")
        print(f"Avg landmark coverage: {avg_landmark_coverage:.1f}%")
        print("=" * 50)

    def _print_processing_stats(self):
        """Print processing statistics"""
        total = self.quality_stats['total_processed']

        print(f"\n📊 Processing Statistics")
        print("=" * 50)
        print(f"Total processed:     {total:6d}")
        print(f"Successful:          {self.quality_stats['successful']:6d} ({self.quality_stats['successful']/total*100:.1f}%)")
        print(f"Invalid crops:       {self.quality_stats['invalid_crop']:6d} ({self.quality_stats['invalid_crop']/total*100:.1f}%)")
        print(f"No landmarks:        {self.quality_stats['no_landmarks']:6d} ({self.quality_stats['no_landmarks']/total*100:.1f}%)")
        print(f"Low confidence:      {self.quality_stats['low_confidence']:6d} ({self.quality_stats['low_confidence']/total*100:.1f}%)")
        print(f"Processing errors:   {self.quality_stats['processing_error']:6d} ({self.quality_stats['processing_error']/total*100:.1f}%)")
        print("=" * 50)


def main():
    parser = argparse.ArgumentParser(description='Preprocess videos using YOLO→SAM pipeline')
    parser.add_argument('--config', default='configs/training.yaml', help='Config file path')
    parser.add_argument('--manifest', required=True, help='Manifest CSV file to process')
    parser.add_argument('--limit_per_class', type=int, help='Limit number of samples per class (for testing)')
    args = parser.parse_args()

    # Load configuration
    config = load_config(args.config)

    # Set seed for reproducibility
    set_seed(config.get('seed', 42))

    # Process videos
    preprocessor = VideoPreprocessor(config)
    preprocessor.process_from_manifest(args.manifest, limit_per_class=args.limit_per_class)


if __name__ == "__main__":
    main()
