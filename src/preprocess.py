"""
Preprocessing Pipeline for ICU Lipreading
YOLO Face Detection → SAM Lip Segmentation → Standardization
"""

import os
import cv2
import numpy as np
import pandas as pd
import tempfile
from pathlib import Path
from typing import List, Dict, Tuple, Optional
import argparse
from tqdm import tqdm
import torch
from ultralytics import YOLO
from segment_anything import SamPredictor, sam_model_registry

from utils import (
    setup_logging, set_seed, load_config, S3Manager,
    create_directories, get_video_info, calculate_motion_score,
    calculate_blur_score, save_frames_as_npy
)


class VideoPreprocessor:
    """Preprocess videos using YOLO→SAM pipeline"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.logger = setup_logging(
            config.get('logging', {}).get('level', 'INFO'),
            config.get('logging', {}).get('log_dir', './logs')
        )
        
        # Configuration
        self.img_size = config['data']['img_size']
        self.frame_length = config['data']['frames_per_clip']
        self.fps_target = config['data']['fps_target']
        self.processed_data_dir = config['data']['data_processed_dir']
        
        # Quality thresholds
        self.min_face_area = config['segmentation']['quality']['min_face_area']
        self.min_motion_threshold = config['segmentation']['quality']['min_motion_threshold']
        self.max_blur_threshold = config['segmentation']['quality']['max_blur_threshold']
        
        # Initialize S3 manager
        self.s3_manager = S3Manager(config['data']['s3_bucket'])
        
        # Create output directories
        create_directories([
            self.processed_data_dir,
            os.path.join(self.processed_data_dir, 'clips'),
            os.path.join(self.processed_data_dir, 'quality_logs')
        ])
        
        # Initialize models
        self._load_models()

        # Quality tracking
        self.quality_stats = {
            'total_processed': 0,
            'successful': 0,
            'invalid_crop': 0,
            'no_landmarks': 0,
            'low_confidence': 0,
            'processing_error': 0
        }
    
    def _load_models(self):
        """Load YOLO, SAM, and MediaPipe models"""
        self.logger.info("🤖 Loading models...")

        # Load YOLO (optional for quality assessment)
        try:
            yolo_model = self.config['segmentation']['yolo']['model']
            self.yolo = YOLO(yolo_model)
            self.yolo_conf = self.config['segmentation']['yolo']['conf_threshold']
            self.logger.info("✅ YOLO loaded")
        except Exception as e:
            self.logger.warning(f"⚠️ YOLO loading failed: {e}")
            self.yolo = None

        # Load SAM (optional, skip if PyTorch 2.6 compatibility issues)
        sam_type = self.config['segmentation']['sam']['model_type']
        sam_checkpoint = self.config['segmentation']['sam']['checkpoint_path']

        if not os.path.exists(sam_checkpoint):
            self.logger.warning(f"⚠️ SAM checkpoint not found: {sam_checkpoint}")
            self.sam_predictor = None
        else:
            try:
                device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
                from utils import load_sam_model
                self.sam_predictor = load_sam_model(sam_type, sam_checkpoint, device)
                self.logger.info("✅ SAM model loaded")
            except Exception as e:
                self.logger.warning(f"⚠️ SAM loading failed: {e}")
                self.sam_predictor = None

        # Load MediaPipe FaceMesh
        try:
            import mediapipe as mp
            self.mp_face_mesh = mp.solutions.face_mesh
            self.face_mesh = self.mp_face_mesh.FaceMesh(
                static_image_mode=True,
                max_num_faces=1,
                refine_landmarks=True,
                min_detection_confidence=0.5,
                min_tracking_confidence=0.5
            )
            self.logger.info("✅ MediaPipe FaceMesh loaded")
            self.mediapipe_available = True
        except ImportError:
            self.logger.warning("⚠️ MediaPipe not available - install with Python 3.11")
            self.face_mesh = None
            self.mediapipe_available = False
        except Exception as e:
            self.logger.warning(f"⚠️ MediaPipe loading failed: {e}")
            self.face_mesh = None
            self.mediapipe_available = False

        self.logger.info("✅ Models loaded successfully")
    
    def extract_frames_from_video(self, video_path: str) -> Optional[np.ndarray]:
        """Extract frames from video file"""
        cap = cv2.VideoCapture(video_path)

        if not cap.isOpened():
            self.logger.error(f"❌ Cannot open video: {video_path}")
            return None

        frames = []
        frame_count = 0
        max_frames = 200  # Limit to prevent memory issues

        while frame_count < max_frames:
            ret, frame = cap.read()
            if not ret:
                break

            # Ensure frame has consistent shape
            if frame is None or frame.size == 0:
                continue

            # Convert BGR to RGB
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

            # Ensure all frames have the same shape
            if len(frames) > 0 and frame_rgb.shape != frames[0].shape:
                # Resize to match first frame
                frame_rgb = cv2.resize(frame_rgb, (frames[0].shape[1], frames[0].shape[0]))

            frames.append(frame_rgb)
            frame_count += 1

        cap.release()

        if not frames:
            return None

        try:
            return np.array(frames)
        except ValueError as e:
            self.logger.error(f"❌ Error creating frame array: {e}")
            return None
    
    def extract_strict_lip_crop(self, frame: np.ndarray) -> tuple:
        """
        Extract strict lip crop using spatial prior heuristic + MediaPipe validation.
        Returns (crop, is_valid, has_landmarks, confidence, adjustment_info)
        """
        h, w = frame.shape[:2]

        # Step 1: Spatial prior heuristic - 3x2 grid, top-middle cell
        col_width = w // 3
        row_height = h // 2

        # Default ROI: top-middle cell (row 0, column 1)
        x1 = col_width
        x2 = 2 * col_width
        y1 = 0
        y2 = row_height

        # Extract initial crop
        initial_crop = frame[y1:y2, x1:x2]

        # Step 2: MediaPipe validation and dynamic adjustment
        if self.mediapipe_available and self.face_mesh is not None:
            is_valid, confidence, has_landmarks, adjustment_needed = self._validate_and_adjust_crop(
                frame, x1, y1, x2, y2
            )

            if adjustment_needed and not is_valid:
                # Try expanding the crop by 20%
                padding_x = int(col_width * 0.2)
                padding_y = int(row_height * 0.2)

                x1_exp = max(0, x1 - padding_x)
                x2_exp = min(w, x2 + padding_x)
                y1_exp = max(0, y1 - padding_y)
                y2_exp = min(h, y2 + padding_y)

                expanded_crop = frame[y1_exp:y2_exp, x1_exp:x2_exp]
                is_valid_exp, confidence_exp, has_landmarks_exp, _ = self._validate_and_adjust_crop(
                    frame, x1_exp, y1_exp, x2_exp, y2_exp
                )

                if is_valid_exp and confidence_exp > confidence:
                    # Use expanded crop
                    final_crop = expanded_crop
                    is_valid, confidence, has_landmarks = is_valid_exp, confidence_exp, has_landmarks_exp
                    adjustment_info = f"expanded_by_{padding_x}x{padding_y}"
                else:
                    # Try vertical shift (move window down slightly)
                    shift_y = row_height // 4
                    y1_shift = min(h - row_height, y1 + shift_y)
                    y2_shift = y1_shift + row_height

                    shifted_crop = frame[y1_shift:y2_shift, x1:x2]
                    is_valid_shift, confidence_shift, has_landmarks_shift, _ = self._validate_and_adjust_crop(
                        frame, x1, y1_shift, x2, y2_shift
                    )

                    if is_valid_shift and confidence_shift > confidence:
                        final_crop = shifted_crop
                        is_valid, confidence, has_landmarks = is_valid_shift, confidence_shift, has_landmarks_shift
                        adjustment_info = f"shifted_down_{shift_y}"
                    else:
                        final_crop = initial_crop
                        adjustment_info = "no_valid_adjustment"
            else:
                final_crop = initial_crop
                adjustment_info = "original_crop"
        else:
            # MediaPipe not available - assume valid
            final_crop = initial_crop
            is_valid, confidence, has_landmarks = True, 1.0, True
            adjustment_info = "mediapipe_unavailable"

        # Resize to target size
        if final_crop.size > 0:
            resized_crop = cv2.resize(final_crop, (self.img_size, self.img_size))
        else:
            # Final fallback: center crop
            crop_size = min(h, w)
            start_h = (h - crop_size) // 2
            start_w = (w - crop_size) // 2
            center_crop = frame[start_h:start_h+crop_size, start_w:start_w+crop_size]
            resized_crop = cv2.resize(center_crop, (self.img_size, self.img_size))
            is_valid, confidence, has_landmarks = False, 0.0, False
            adjustment_info = "fallback_center_crop"

        return resized_crop, is_valid, has_landmarks, confidence, adjustment_info

    def _validate_and_adjust_crop(self, frame: np.ndarray, x1: int, y1: int, x2: int, y2: int) -> tuple:
        """
        Validate crop region using MediaPipe and determine if adjustment is needed.
        Returns (is_valid, confidence, has_landmarks, adjustment_needed)
        """
        if not self.mediapipe_available or self.face_mesh is None:
            return True, 1.0, True, False

        try:
            # Extract crop region
            crop = frame[y1:y2, x1:x2]

            # Convert to RGB if needed
            if len(crop.shape) == 3 and crop.shape[2] == 3:
                rgb_crop = crop
            else:
                rgb_crop = cv2.cvtColor(crop, cv2.COLOR_BGR2RGB)

            # Convert to uint8 if needed
            if rgb_crop.dtype == np.float32:
                rgb_crop = (rgb_crop * 255).astype(np.uint8)

            # Run MediaPipe face detection
            results = self.face_mesh.process(rgb_crop)

            if not results.multi_face_landmarks:
                return False, 0.0, False, True

            # Get the first face
            face_landmarks = results.multi_face_landmarks[0]

            # Define lip landmark indices (MediaPipe 468-point model)
            # Upper lip: 13, 82, 18, 17, 16, 15, 14
            # Lower lip: 18, 175, 199, 200, 16, 17, 18
            # Mouth corners: 61, 291
            # More comprehensive lip region
            lip_landmark_indices = [
                # Upper lip outer
                61, 84, 17, 314, 405, 320, 307, 375, 321, 308, 324, 318,
                # Lower lip outer
                61, 146, 91, 181, 84, 17, 314, 405, 320, 307, 375, 321,
                # Inner lips
                78, 95, 88, 178, 87, 14, 317, 402, 318, 324,
                # Mouth corners
                61, 291
            ]

            # Remove duplicates and ensure valid indices
            lip_landmark_indices = list(set([idx for idx in lip_landmark_indices if idx < 468]))

            # Count valid lip landmarks within crop
            valid_lip_landmarks = 0
            total_lip_landmarks = len(lip_landmark_indices)

            h, w = rgb_crop.shape[:2]

            for idx in lip_landmark_indices:
                if idx < len(face_landmarks.landmark):
                    landmark = face_landmarks.landmark[idx]
                    x = landmark.x * w
                    y = landmark.y * h

                    # Check if landmark is within crop bounds with small margin
                    margin = 5  # pixels
                    if margin <= x <= w - margin and margin <= y <= h - margin:
                        valid_lip_landmarks += 1

            # Calculate confidence
            confidence = valid_lip_landmarks / total_lip_landmarks if total_lip_landmarks > 0 else 0.0

            # Consider valid if >70% of lip landmarks are present
            is_valid = confidence >= 0.7
            adjustment_needed = not is_valid

            return is_valid, confidence, True, adjustment_needed

        except Exception as e:
            self.logger.debug(f"MediaPipe validation failed: {e}")
            return False, 0.0, False, True
    
    def segment_lips_with_sam(self, frame: np.ndarray, face_bbox: Tuple[int, int, int, int]) -> Optional[np.ndarray]:
        """Segment lips using SAM within face bounding box"""
        if self.sam_predictor is None:
            return None

        x1, y1, x2, y2 = face_bbox

        # Extract face region
        face_crop = frame[y1:y2, x1:x2]

        if face_crop.size == 0:
            return None

        try:
            # Set image for SAM
            self.sam_predictor.set_image(face_crop)

            # Create prompt point in lower center of face (approximate lip location)
            face_h, face_w = face_crop.shape[:2]
            lip_point = np.array([[face_w // 2, int(face_h * 0.75)]])  # Lower center
            lip_label = np.array([1])

            # Generate mask
            masks, scores, _ = self.sam_predictor.predict(
                point_coords=lip_point,
                point_labels=lip_label,
                multimask_output=True
            )

            if len(masks) == 0:
                return None

            # Select best mask
            best_mask = masks[np.argmax(scores)]

            # Apply mask to face crop
            masked_face = face_crop.copy()
            masked_face[~best_mask] = 0  # Set non-lip regions to black

            return masked_face

        except Exception as e:
            self.logger.debug(f"SAM segmentation failed: {e}")
            return None
    
    def standardize_clip(self, frames: np.ndarray) -> Optional[np.ndarray]:
        """Standardize clip to fixed size and frame count"""
        if len(frames) == 0:
            return None
        
        # Temporal standardization: sample to target frame count
        current_frames = len(frames)
        
        if current_frames < self.frame_length:
            # Pad by repeating last frame
            padding_needed = self.frame_length - current_frames
            last_frame = frames[-1:].repeat(padding_needed, axis=0)
            frames = np.concatenate([frames, last_frame], axis=0)
        elif current_frames > self.frame_length:
            # Uniform sampling
            indices = np.linspace(0, current_frames - 1, self.frame_length, dtype=int)
            frames = frames[indices]
        
        # Spatial standardization: resize to target size
        standardized_frames = []
        for frame in frames:
            if frame.shape[:2] != (self.img_size, self.img_size):
                resized = cv2.resize(frame, (self.img_size, self.img_size))
                standardized_frames.append(resized)
            else:
                standardized_frames.append(frame)
        
        standardized_frames = np.array(standardized_frames)
        
        # Pixel normalization: scale to [0, 1]
        standardized_frames = standardized_frames.astype(np.float32) / 255.0
        
        return standardized_frames
    


    def process_single_video(self, s3_key: str, output_name: str) -> Dict[str, any]:
        """Process a single video through the complete pipeline"""
        self.quality_stats['total_processed'] += 1

        # Download video to temporary file
        with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as tmp_file:
            temp_path = tmp_file.name

        try:
            # Download from S3
            if not self.s3_manager.download_video(s3_key, temp_path):
                self.quality_stats['processing_error'] += 1
                return {'success': False, 'error': 'download_failed'}

            # Extract frames
            frames = self.extract_frames_from_video(temp_path)
            if frames is None:
                self.quality_stats['processing_error'] += 1
                return {'success': False, 'error': 'frame_extraction_failed'}

            # Process each frame using strict cropping with MediaPipe validation
            processed_frames = []
            validation_results = []

            for frame in frames:
                # Apply strict lip crop with MediaPipe validation and dynamic adjustment
                lip_crop, is_valid, has_landmarks, confidence, adjustment_info = self.extract_strict_lip_crop(frame)

                validation_results.append({
                    'valid': is_valid,
                    'confidence': confidence,
                    'has_landmarks': has_landmarks,
                    'adjustment': adjustment_info
                })

                processed_frames.append(lip_crop)

            if not processed_frames:
                self.quality_stats['processing_error'] += 1
                return {'success': False, 'error': 'no_frames_processed'}

            # Convert to numpy array - all frames should now have the same shape
            try:
                processed_frames = np.array(processed_frames)
            except ValueError as e:
                self.quality_stats['processing_error'] += 1
                return {'success': False, 'error': f'frame_array_creation_failed: {e}'}

            # Quality assessment based on MediaPipe validation
            valid_frames = sum(1 for r in validation_results if r['valid'])
            total_frames = len(validation_results)
            frames_with_landmarks = sum(1 for r in validation_results if r['has_landmarks'])
            avg_confidence = np.mean([r['confidence'] for r in validation_results])

            # Check if clip passes quality filters
            valid_frame_ratio = valid_frames / total_frames if total_frames > 0 else 0

            if valid_frame_ratio < 0.5:  # Less than 50% of frames have valid lip crops
                self.quality_stats['invalid_crop'] += 1
                return {
                    'success': False,
                    'error': 'invalid_crop',
                    'valid_frames': valid_frames,
                    'total_frames': total_frames,
                    'avg_confidence': avg_confidence
                }

            if frames_with_landmarks == 0:  # No frames have any landmarks
                self.quality_stats['no_landmarks'] += 1
                return {
                    'success': False,
                    'error': 'no_landmarks',
                    'valid_frames': valid_frames,
                    'total_frames': total_frames,
                    'avg_confidence': avg_confidence
                }

            if avg_confidence < 0.3:  # Very low confidence overall
                self.quality_stats['low_confidence'] += 1
                return {
                    'success': False,
                    'error': 'low_confidence',
                    'valid_frames': valid_frames,
                    'total_frames': total_frames,
                    'avg_confidence': avg_confidence
                }

            # Standardize clip
            standardized_clip = self.standardize_clip(processed_frames)
            if standardized_clip is None:
                self.quality_stats['processing_error'] += 1
                return {'success': False, 'error': 'standardization_failed'}

            # Save processed clip
            output_path = os.path.join(self.processed_data_dir, 'clips', f"{output_name}.npy")
            save_frames_as_npy(standardized_clip, output_path)

            self.quality_stats['successful'] += 1

            return {
                'success': True,
                'output_path': output_path,
                'original_frames': len(frames),
                'processed_frames': len(standardized_clip),
                'valid_frames': valid_frames,
                'total_frames': total_frames,
                'avg_confidence': avg_confidence,
                'has_landmarks': frames_with_landmarks > 0,
                'valid_crop': valid_frame_ratio >= 0.5
            }

        except Exception as e:
            self.logger.error(f"❌ Error processing {s3_key}: {e}")
            self.quality_stats['processing_error'] += 1
            return {'success': False, 'error': str(e)}

        finally:
            # Clean up temporary file
            if os.path.exists(temp_path):
                os.unlink(temp_path)

    def process_from_manifest(self, manifest_path: str, limit_per_class: Optional[int] = None):
        """Process all videos listed in a manifest file"""
        self.logger.info(f"📋 Processing videos from manifest: {manifest_path}")

        # Load manifest
        df = pd.read_csv(manifest_path)

        # Apply limit per class if specified
        if limit_per_class is not None:
            self.logger.info(f"🔢 Limiting to {limit_per_class} samples per class")
            limited_dfs = []
            for phrase in df['phrase'].unique():
                phrase_df = df[df['phrase'] == phrase].head(limit_per_class)
                limited_dfs.append(phrase_df)
            df = pd.concat(limited_dfs, ignore_index=True)
            self.logger.info(f"📊 Limited dataset size: {len(df)} samples")

        # Process each video
        results = []

        for idx, row in tqdm(df.iterrows(), total=len(df), desc="Processing videos"):
            s3_key = row['s3_key']
            phrase = row['phrase']
            speaker_id = row['speaker_id']

            # Generate output name
            output_name = f"{phrase}_{speaker_id}_{idx:06d}"

            # Process video
            result = self.process_single_video(s3_key, output_name)

            # Add metadata
            result.update({
                'manifest_idx': idx,
                's3_key': s3_key,
                'phrase': phrase,
                'speaker_id': speaker_id,
                'output_name': output_name
            })

            results.append(result)

        # Save processing log
        log_name = os.path.basename(manifest_path).replace('.csv', '_processing_log.csv')
        log_path = os.path.join(self.processed_data_dir, 'quality_logs', log_name)

        results_df = pd.DataFrame(results)
        results_df.to_csv(log_path, index=False)

        # Print statistics
        self._print_processing_stats()

        self.logger.info(f"✅ Processing completed. Log saved to: {log_path}")

    def _print_processing_stats(self):
        """Print processing statistics"""
        total = self.quality_stats['total_processed']

        print(f"\n📊 Processing Statistics")
        print("=" * 50)
        print(f"Total processed:     {total:6d}")
        print(f"Successful:          {self.quality_stats['successful']:6d} ({self.quality_stats['successful']/total*100:.1f}%)")
        print(f"Invalid crops:       {self.quality_stats['invalid_crop']:6d} ({self.quality_stats['invalid_crop']/total*100:.1f}%)")
        print(f"No landmarks:        {self.quality_stats['no_landmarks']:6d} ({self.quality_stats['no_landmarks']/total*100:.1f}%)")
        print(f"Low confidence:      {self.quality_stats['low_confidence']:6d} ({self.quality_stats['low_confidence']/total*100:.1f}%)")
        print(f"Processing errors:   {self.quality_stats['processing_error']:6d} ({self.quality_stats['processing_error']/total*100:.1f}%)")
        print("=" * 50)


def main():
    parser = argparse.ArgumentParser(description='Preprocess videos using YOLO→SAM pipeline')
    parser.add_argument('--config', default='configs/training.yaml', help='Config file path')
    parser.add_argument('--manifest', required=True, help='Manifest CSV file to process')
    parser.add_argument('--limit_per_class', type=int, help='Limit number of samples per class (for testing)')
    args = parser.parse_args()

    # Load configuration
    config = load_config(args.config)

    # Set seed for reproducibility
    set_seed(config.get('seed', 42))

    # Process videos
    preprocessor = VideoPreprocessor(config)
    preprocessor.process_from_manifest(args.manifest, limit_per_class=args.limit_per_class)


if __name__ == "__main__":
    main()
