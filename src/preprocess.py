"""
Preprocessing Pipeline for ICU Lipreading
YOLO Face Detection → SAM Lip Segmentation → Standardization
"""

import os
import cv2
import numpy as np
import pandas as pd
import tempfile
from pathlib import Path
from typing import List, Dict, Tuple, Optional
import argparse
from tqdm import tqdm
import torch
from ultralytics import YOLO
from segment_anything import SamPredictor, sam_model_registry

from utils import (
    setup_logging, set_seed, load_config, S3Manager,
    create_directories, get_video_info, calculate_motion_score,
    calculate_blur_score, save_frames_as_npy
)


class VideoPreprocessor:
    """Preprocess videos using YOLO→SAM pipeline"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.logger = setup_logging(
            config.get('logging', {}).get('level', 'INFO'),
            config.get('logging', {}).get('log_dir', './logs')
        )
        
        # Configuration
        self.img_size = config['data']['img_size']
        self.frame_length = config['data']['frames_per_clip']
        self.fps_target = config['data']['fps_target']
        self.processed_data_dir = config['data']['data_processed_dir']
        
        # Quality thresholds
        self.min_face_area = config['segmentation']['quality']['min_face_area']
        self.min_motion_threshold = config['segmentation']['quality']['min_motion_threshold']
        self.max_blur_threshold = config['segmentation']['quality']['max_blur_threshold']
        
        # Initialize S3 manager
        self.s3_manager = S3Manager(config['data']['s3_bucket'])
        
        # Create output directories
        create_directories([
            self.processed_data_dir,
            os.path.join(self.processed_data_dir, 'clips'),
            os.path.join(self.processed_data_dir, 'quality_logs')
        ])
        
        # Initialize models
        self._load_models()

        # Quality tracking
        self.quality_stats = {
            'total_processed': 0,
            'successful': 0,
            'invalid_crop': 0,
            'no_landmarks': 0,
            'low_confidence': 0,
            'processing_error': 0
        }
    
    def _load_models(self):
        """Load YOLO, SAM, and MediaPipe models"""
        self.logger.info("🤖 Loading models...")

        # Load YOLO (optional for quality assessment)
        try:
            yolo_model = self.config['segmentation']['yolo']['model']
            self.yolo = YOLO(yolo_model)
            self.yolo_conf = self.config['segmentation']['yolo']['conf_threshold']
            self.logger.info("✅ YOLO loaded")
        except Exception as e:
            self.logger.warning(f"⚠️ YOLO loading failed: {e}")
            self.yolo = None

        # Load SAM (optional, skip if PyTorch 2.6 compatibility issues)
        sam_type = self.config['segmentation']['sam']['model_type']
        sam_checkpoint = self.config['segmentation']['sam']['checkpoint_path']

        if not os.path.exists(sam_checkpoint):
            self.logger.warning(f"⚠️ SAM checkpoint not found: {sam_checkpoint}")
            self.sam_predictor = None
        else:
            try:
                device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
                from utils import load_sam_model
                self.sam_predictor = load_sam_model(sam_type, sam_checkpoint, device)
                self.logger.info("✅ SAM model loaded")
            except Exception as e:
                self.logger.warning(f"⚠️ SAM loading failed: {e}")
                self.sam_predictor = None

        # Load MediaPipe FaceMesh
        try:
            import mediapipe as mp
            self.mp_face_mesh = mp.solutions.face_mesh
            self.face_mesh = self.mp_face_mesh.FaceMesh(
                static_image_mode=True,
                max_num_faces=1,
                refine_landmarks=True,
                min_detection_confidence=0.5,
                min_tracking_confidence=0.5
            )
            self.logger.info("✅ MediaPipe FaceMesh loaded")
            self.mediapipe_available = True
        except ImportError:
            self.logger.warning("⚠️ MediaPipe not available - install with Python 3.11")
            self.face_mesh = None
            self.mediapipe_available = False
        except Exception as e:
            self.logger.warning(f"⚠️ MediaPipe loading failed: {e}")
            self.face_mesh = None
            self.mediapipe_available = False

        self.logger.info("✅ Models loaded successfully")

        # Initialize quality tracking CSV
        self.quality_records = []
    
    def extract_frames_from_video(self, video_path: str) -> Optional[np.ndarray]:
        """Extract frames from video file"""
        cap = cv2.VideoCapture(video_path)

        if not cap.isOpened():
            self.logger.error(f"❌ Cannot open video: {video_path}")
            return None

        frames = []
        frame_count = 0
        max_frames = 200  # Limit to prevent memory issues

        while frame_count < max_frames:
            ret, frame = cap.read()
            if not ret:
                break

            # Ensure frame has consistent shape
            if frame is None or frame.size == 0:
                continue

            # Convert BGR to RGB
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

            # Ensure all frames have the same shape
            if len(frames) > 0 and frame_rgb.shape != frames[0].shape:
                # Resize to match first frame
                frame_rgb = cv2.resize(frame_rgb, (frames[0].shape[1], frames[0].shape[0]))

            frames.append(frame_rgb)
            frame_count += 1

        cap.release()

        if not frames:
            return None

        try:
            return np.array(frames)
        except ValueError as e:
            self.logger.error(f"❌ Error creating frame array: {e}")
            return None
    
    def extract_landmark_anchored_lip_crop(self, frames: list) -> tuple:
        """
        Extract lip crops using landmark-anchored, scale-controlled approach with temporal stability.

        1. Use MediaPipe FaceMesh to get lip landmarks and mouth width
        2. Target coverage: mouth width should occupy 60-75% of crop width after resize to 96x96
        3. Apply temporal smoothing across frames
        4. Auto-adjust crop size based on lip coverage
        5. Fallback to YOLO+grid if MediaPipe fails

        Returns (processed_frames, validation_results, crop_metrics)
        """
        # Configuration from training.yaml (hardcoded for now)
        config = {
            'target_mouth_width_ratio': 0.70,
            'aspect_ratio_y': 1.0,
            'pad_pct': 0.10,
            'coverage_accept_min': 0.30,
            'coverage_accept_max': 0.75,
            'smooth_window': 5,
            'min_frames_with_landmarks_pct': 90
        }

        processed_frames = []
        validation_results = []
        frame_metrics = []

        # Step 1: Extract lip landmarks and compute mouth width for each frame
        raw_landmarks = []
        mouth_widths = []
        lip_centroids = []

        for frame_idx, frame in enumerate(frames):
            landmarks, mouth_width, lip_centroid = self._extract_lip_landmarks_and_metrics(frame)
            raw_landmarks.append(landmarks)
            mouth_widths.append(mouth_width if mouth_width > 0 else None)
            lip_centroids.append(lip_centroid)

        # Step 2: Apply temporal smoothing
        smoothed_mouth_widths = self._apply_temporal_smoothing(mouth_widths, config['smooth_window'])
        smoothed_centroids = self._apply_centroid_smoothing(lip_centroids, config['smooth_window'])

        # Step 3: Process each frame with smoothed parameters
        for frame_idx, frame in enumerate(frames):
            landmarks = raw_landmarks[frame_idx]
            mouth_width = smoothed_mouth_widths[frame_idx]
            lip_centroid = smoothed_centroids[frame_idx]

            if landmarks is not None and mouth_width is not None and lip_centroid is not None:
                # Primary path: landmark-anchored cropping
                crop, metrics = self._create_landmark_anchored_crop(
                    frame, landmarks, mouth_width, lip_centroid, config
                )

                validation_results.append({
                    'valid': metrics['valid'],
                    'confidence': metrics['confidence'],
                    'has_landmarks': True,
                    'adjustment': metrics['method'],
                    'coverage_before': metrics['coverage_before'],
                    'coverage_after': metrics['coverage_after'],
                    'adjustments_made': metrics['adjustments_made']
                })

                frame_metrics.append(metrics)
                processed_frames.append(crop)

            else:
                # Fallback path: YOLO + grid approach
                crop, fallback_metrics = self._fallback_crop_with_validation(frame, config)

                validation_results.append({
                    'valid': False,
                    'confidence': 0.3,
                    'has_landmarks': False,
                    'adjustment': 'fallback_no_landmarks',
                    'coverage_before': 0.0,
                    'coverage_after': 0.0,
                    'adjustments_made': 0
                })

                frame_metrics.append(fallback_metrics)
                processed_frames.append(crop)

        # Step 4: Compute clip-level metrics
        frames_with_landmarks = sum(1 for r in validation_results if r['has_landmarks'])
        frames_with_landmarks_pct = (frames_with_landmarks / len(frames) * 100) if frames else 0

        # Determine if clip is valid based on landmark coverage threshold
        is_valid_clip = frames_with_landmarks_pct >= config['min_frames_with_landmarks_pct']

        clip_metrics = {
            'frames_with_landmarks_pct': frames_with_landmarks_pct,
            'valid_clip': is_valid_clip,
            'avg_mouth_width': np.mean([m for m in mouth_widths if m is not None]) if any(m for m in mouth_widths if m is not None) else 0,
            'avg_coverage_after': np.mean([m['coverage_after'] for m in frame_metrics]),
            'frame_metrics': frame_metrics
        }

        return processed_frames, validation_results, clip_metrics

    def _extract_lip_landmarks_and_metrics(self, frame: np.ndarray) -> tuple:
        """
        Extract lip landmarks and compute mouth width and centroid.
        Returns (landmarks, mouth_width, lip_centroid) or (None, None, None) if failed
        """
        if not self.mediapipe_available or self.face_mesh is None:
            return None, None, None

        try:
            # Convert to RGB
            rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB) if len(frame.shape) == 3 else frame
            if rgb_frame.dtype == np.float32:
                rgb_frame = (rgb_frame * 255).astype(np.uint8)

            # Run MediaPipe
            results = self.face_mesh.process(rgb_frame)

            if not results.multi_face_landmarks:
                return None, None, None

            face_landmarks = results.multi_face_landmarks[0]
            h, w = rgb_frame.shape[:2]

            # Define lip landmark indices (MediaPipe 468-point model)
            # Outer lip contour for mouth width calculation
            outer_lip_indices = [61, 146, 91, 181, 84, 17, 314, 405, 320, 307, 375, 321, 308, 324, 318, 402, 317, 14, 87, 178, 88, 95]
            # Mouth corners for width calculation
            left_mouth_corner = 61
            right_mouth_corner = 291

            # Extract lip points
            lip_points = []
            for idx in outer_lip_indices:
                if idx < len(face_landmarks.landmark):
                    landmark = face_landmarks.landmark[idx]
                    x = landmark.x * w
                    y = landmark.y * h
                    lip_points.append((x, y))

            if len(lip_points) < 4:
                return None, None, None

            # Calculate mouth width (distance between mouth corners)
            if left_mouth_corner < len(face_landmarks.landmark) and right_mouth_corner < len(face_landmarks.landmark):
                left_corner = face_landmarks.landmark[left_mouth_corner]
                right_corner = face_landmarks.landmark[right_mouth_corner]

                left_x, left_y = left_corner.x * w, left_corner.y * h
                right_x, right_y = right_corner.x * w, right_corner.y * h

                mouth_width = np.sqrt((right_x - left_x)**2 + (right_y - left_y)**2)
            else:
                # Fallback: use bounding box width of lip points
                lip_points_array = np.array(lip_points)
                mouth_width = np.max(lip_points_array[:, 0]) - np.min(lip_points_array[:, 0])

            # Calculate lip centroid
            lip_points_array = np.array(lip_points)
            lip_centroid = np.mean(lip_points_array, axis=0)

            return face_landmarks, mouth_width, lip_centroid

        except Exception as e:
            self.logger.debug(f"Landmark extraction failed: {e}")
            return None, None, None

    def _apply_temporal_smoothing(self, mouth_widths: list, window_size: int) -> list:
        """Apply median smoothing to mouth widths across frames"""
        smoothed = []
        valid_widths = [w for w in mouth_widths if w is not None]

        if not valid_widths:
            return mouth_widths

        median_width = np.median(valid_widths)

        for i, width in enumerate(mouth_widths):
            if width is not None:
                # Use median of surrounding frames
                start_idx = max(0, i - window_size // 2)
                end_idx = min(len(mouth_widths), i + window_size // 2 + 1)
                window_widths = [w for w in mouth_widths[start_idx:end_idx] if w is not None]

                if window_widths:
                    smoothed.append(np.median(window_widths))
                else:
                    smoothed.append(median_width)
            else:
                # Use last valid width or median
                if smoothed and smoothed[-1] is not None:
                    smoothed.append(smoothed[-1])
                else:
                    smoothed.append(median_width)

        return smoothed

    def _apply_centroid_smoothing(self, centroids: list, window_size: int) -> list:
        """Apply smoothing to lip centroids across frames"""
        smoothed = []
        valid_centroids = [c for c in centroids if c is not None]

        if not valid_centroids:
            return centroids

        median_centroid = np.median(valid_centroids, axis=0)

        for i, centroid in enumerate(centroids):
            if centroid is not None:
                # Use median of surrounding frames
                start_idx = max(0, i - window_size // 2)
                end_idx = min(len(centroids), i + window_size // 2 + 1)
                window_centroids = [c for c in centroids[start_idx:end_idx] if c is not None]

                if window_centroids:
                    smoothed.append(np.median(window_centroids, axis=0))
                else:
                    smoothed.append(median_centroid)
            else:
                # Use last valid centroid or median
                if smoothed and smoothed[-1] is not None:
                    smoothed.append(smoothed[-1])
                else:
                    smoothed.append(median_centroid)

        return smoothed

    def _validate_geometric_lip_crop(self, face_bbox, crop_bbox, face_confidence) -> tuple:
        """
        Validate geometric lip crop based on face detection confidence and geometric constraints.
        Returns (is_valid, confidence)
        """
        try:
            if face_bbox is None:
                return False, 0.0

            fx1, fy1, fx2, fy2 = face_bbox
            cx1, cy1, cx2, cy2 = crop_bbox

            face_w = fx2 - fx1
            face_h = fy2 - fy1
            crop_w = cx2 - cx1
            crop_h = cy2 - cy1

            # Base confidence from face detection
            confidence = face_confidence

            # Geometric validation checks

            # 1. Check if crop is reasonably positioned within face
            crop_center_x = (cx1 + cx2) // 2
            crop_center_y = (cy1 + cy2) // 2
            face_center_x = (fx1 + fx2) // 2
            face_center_y = (fy1 + fy2) // 2

            # Crop should be in lower half of face (lips are in lower face)
            if crop_center_y < face_center_y:
                confidence *= 0.7  # Penalty for crop in upper face

            # Crop should be horizontally centered relative to face
            horizontal_offset = abs(crop_center_x - face_center_x) / face_w
            if horizontal_offset > 0.3:  # More than 30% offset from face center
                confidence *= (1.0 - horizontal_offset)

            # 2. Check crop size relative to face size
            crop_to_face_ratio_w = crop_w / face_w
            crop_to_face_ratio_h = crop_h / face_h

            # Ideal lip crop should be 30-60% of face width and 10-25% of face height
            if not (0.2 <= crop_to_face_ratio_w <= 0.8):
                confidence *= 0.8  # Penalty for unusual width ratio

            if not (0.08 <= crop_to_face_ratio_h <= 0.4):
                confidence *= 0.8  # Penalty for unusual height ratio

            # 3. Check minimum crop size (avoid too small crops)
            min_crop_size = 24
            if crop_w < min_crop_size or crop_h < min_crop_size:
                confidence *= 0.5  # Heavy penalty for too small crops

            # 4. Face detection confidence threshold
            if face_confidence < 0.5:
                confidence *= 0.6  # Penalty for low face detection confidence

            # Consider valid if confidence >= 90% (high threshold for landmark-driven approach)
            is_valid = confidence >= 0.9

            return is_valid, confidence

        except Exception as e:
            self.logger.debug(f"Geometric validation failed: {e}")
            return False, 0.0

    def _create_landmark_anchored_crop(self, frame: np.ndarray, landmarks, mouth_width: float,
                                     lip_centroid: np.ndarray, config: dict) -> tuple:
        """
        Create landmark-anchored crop with scale control and auto-adjustment.
        Returns (crop, metrics)
        """
        h, w = frame.shape[:2]

        # Calculate initial crop dimensions based on mouth width
        target_ratio = config['target_mouth_width_ratio']
        aspect_ratio_y = config['aspect_ratio_y']
        pad_pct = config['pad_pct']

        # Target crop width: mouth_width / target_ratio
        crop_width = mouth_width / target_ratio
        crop_height = crop_width * aspect_ratio_y

        # Center crop on lip centroid
        center_x, center_y = lip_centroid

        # Initial crop bounds
        x1 = int(center_x - crop_width / 2)
        x2 = int(center_x + crop_width / 2)
        y1 = int(center_y - crop_height / 2)
        y2 = int(center_y + crop_height / 2)

        # Add padding
        pad_x = int(crop_width * pad_pct)
        pad_y = int(crop_height * pad_pct)

        x1_pad = max(0, x1 - pad_x)
        x2_pad = min(w, x2 + pad_x)
        y1_pad = max(0, y1 - pad_y)
        y2_pad = min(h, y2 + pad_y)

        # Extract initial crop
        initial_crop = frame[y1_pad:y2_pad, x1_pad:x2_pad]

        if initial_crop.size == 0:
            # Fallback to center crop
            crop_size = min(h, w) // 2
            center_h, center_w = h // 2, w // 2
            fallback_crop = frame[center_h-crop_size//2:center_h+crop_size//2,
                                center_w-crop_size//2:center_w+crop_size//2]
            resized_crop = cv2.resize(fallback_crop, (self.img_size, self.img_size))

            return resized_crop, {
                'valid': False,
                'confidence': 0.0,
                'method': 'fallback_bounds_error',
                'coverage_before': 0.0,
                'coverage_after': 0.0,
                'adjustments_made': 0,
                'mouth_width_px': mouth_width,
                'crop_w_px': 0,
                'target_ratio': target_ratio
            }

        # Resize to 96x96 for coverage calculation
        resized_crop = cv2.resize(initial_crop, (self.img_size, self.img_size))

        # Calculate initial lip coverage
        coverage_before = self._calculate_lip_coverage(landmarks, (x1_pad, y1_pad, x2_pad, y2_pad), frame.shape)

        # Auto-adjust crop size based on coverage
        final_crop = resized_crop
        coverage_after = coverage_before
        adjustments_made = 0
        final_crop_w_px = x2_pad - x1_pad

        coverage_min = config['coverage_accept_min']
        coverage_max = config['coverage_accept_max']

        if coverage_before < coverage_min or coverage_before > coverage_max:
            # Need to adjust crop size
            for attempt in range(3):  # Up to 3 adjustment attempts
                if coverage_after < coverage_min:
                    # Crop too loose - reduce crop width by 10%
                    crop_width *= 0.9
                elif coverage_after > coverage_max:
                    # Crop too tight - increase crop width by 10%
                    crop_width *= 1.1

                # Recalculate crop bounds
                x1 = int(center_x - crop_width / 2)
                x2 = int(center_x + crop_width / 2)
                y1 = int(center_y - crop_height / 2)
                y2 = int(center_y + crop_height / 2)

                # Add padding
                pad_x = int(crop_width * pad_pct)
                pad_y = int(crop_height * pad_pct)

                x1_pad = max(0, x1 - pad_x)
                x2_pad = min(w, x2 + pad_x)
                y1_pad = max(0, y1 - pad_y)
                y2_pad = min(h, y2 + pad_y)

                # Extract adjusted crop
                adjusted_crop = frame[y1_pad:y2_pad, x1_pad:x2_pad]

                if adjusted_crop.size > 0:
                    final_crop = cv2.resize(adjusted_crop, (self.img_size, self.img_size))
                    coverage_after = self._calculate_lip_coverage(landmarks, (x1_pad, y1_pad, x2_pad, y2_pad), frame.shape)
                    final_crop_w_px = x2_pad - x1_pad
                    adjustments_made += 1

                    # Check if coverage is now acceptable
                    if coverage_min <= coverage_after <= coverage_max:
                        break
                else:
                    break

        # Determine validity
        is_valid = coverage_min <= coverage_after <= coverage_max
        confidence = min(1.0, coverage_after / coverage_max) if coverage_after <= coverage_max else max(0.0, 1.0 - (coverage_after - coverage_max))

        metrics = {
            'valid': is_valid,
            'confidence': confidence,
            'method': 'landmark_anchored',
            'coverage_before': coverage_before,
            'coverage_after': coverage_after,
            'adjustments_made': adjustments_made,
            'mouth_width_px': mouth_width,
            'crop_w_px': final_crop_w_px,
            'target_ratio': target_ratio
        }

        return final_crop, metrics

    def _calculate_lip_coverage(self, landmarks, crop_bbox: tuple, frame_shape: tuple) -> float:
        """
        Calculate lip coverage as percentage of crop area occupied by lip polygon.
        Returns coverage ratio (0.0 to 1.0)
        """
        if landmarks is None:
            return 0.0

        try:
            h, w = frame_shape[:2]
            x1, y1, x2, y2 = crop_bbox
            crop_w = x2 - x1
            crop_h = y2 - y1

            if crop_w <= 0 or crop_h <= 0:
                return 0.0

            # Define lip landmark indices for coverage calculation
            lip_indices = [61, 146, 91, 181, 84, 17, 314, 405, 320, 307, 375, 321, 308, 324, 318, 402, 317, 14, 87, 178, 88, 95]

            # Extract lip points in original frame coordinates
            lip_points = []
            for idx in lip_indices:
                if idx < len(landmarks.landmark):
                    landmark = landmarks.landmark[idx]
                    x = landmark.x * w
                    y = landmark.y * h
                    lip_points.append((x, y))

            if len(lip_points) < 3:
                return 0.0

            # Convert to crop coordinates
            crop_lip_points = []
            for x, y in lip_points:
                if x1 <= x <= x2 and y1 <= y <= y2:
                    crop_x = (x - x1) / crop_w * self.img_size
                    crop_y = (y - y1) / crop_h * self.img_size
                    crop_lip_points.append((int(crop_x), int(crop_y)))

            if len(crop_lip_points) < 3:
                return 0.0

            # Create convex hull of lip points
            hull_points = cv2.convexHull(np.array(crop_lip_points, dtype=np.int32))

            # Calculate area of convex hull
            lip_area = cv2.contourArea(hull_points)

            # Total crop area
            crop_area = self.img_size * self.img_size

            # Coverage ratio
            coverage = lip_area / crop_area

            return min(1.0, max(0.0, coverage))

        except Exception as e:
            self.logger.debug(f"Coverage calculation failed: {e}")
            return 0.0

    def _fallback_crop_with_validation(self, frame: np.ndarray, config: dict) -> tuple:
        """
        Fallback cropping when MediaPipe fails: YOLO + grid approach
        Returns (crop, metrics)
        """
        h, w = frame.shape[:2]

        # Try YOLO face detection first
        face_bbox = None
        if self.yolo is not None:
            try:
                results = self.yolo(frame, conf=self.yolo_conf, classes=[0], verbose=False)
                if results and results[0].boxes:
                    boxes = results[0].boxes.xyxy.cpu().numpy()
                    if len(boxes) > 0:
                        box = boxes[0]  # Take first detection
                        x1, y1, x2, y2 = map(int, box)
                        face_bbox = (x1, y1, x2, y2)
            except Exception as e:
                self.logger.debug(f"YOLO fallback failed: {e}")

        if face_bbox is not None:
            # Apply 3x2 grid within face bbox - top-middle cell
            fx1, fy1, fx2, fy2 = face_bbox
            face_w = fx2 - fx1
            face_h = fy2 - fy1

            col_width = face_w // 3
            row_height = face_h // 2

            # Top-middle cell
            x1 = fx1 + col_width
            x2 = fx1 + 2 * col_width
            y1 = fy1
            y2 = fy1 + row_height

            # Ensure bounds
            x1 = max(0, min(x1, w-1))
            x2 = max(x1+1, min(x2, w))
            y1 = max(0, min(y1, h-1))
            y2 = max(y1+1, min(y2, h))

            # Extract crop
            crop = frame[y1:y2, x1:x2]

            if crop.size > 0:
                resized_crop = cv2.resize(crop, (self.img_size, self.img_size))

                # Try MediaPipe again on the cropped region
                landmarks, _, _ = self._extract_lip_landmarks_and_metrics(resized_crop)

                if landmarks is not None:
                    method = "fallback_face_grid_with_landmarks"
                    confidence = 0.6
                else:
                    method = "fallback_face_grid_no_landmarks"
                    confidence = 0.3

                metrics = {
                    'valid': landmarks is not None,
                    'confidence': confidence,
                    'method': method,
                    'coverage_before': 0.0,
                    'coverage_after': 0.0,
                    'adjustments_made': 0,
                    'mouth_width_px': 0,
                    'crop_w_px': x2 - x1,
                    'target_ratio': 0.0
                }

                return resized_crop, metrics

        # Final fallback: center crop
        crop_size = min(h, w) // 2
        center_h, center_w = h // 2, w // 2
        center_crop = frame[center_h-crop_size//2:center_h+crop_size//2,
                          center_w-crop_size//2:center_w+crop_size//2]

        if center_crop.size > 0:
            resized_crop = cv2.resize(center_crop, (self.img_size, self.img_size))
        else:
            # Ultimate fallback: black image
            resized_crop = np.zeros((self.img_size, self.img_size, 3), dtype=np.uint8)

        metrics = {
            'valid': False,
            'confidence': 0.1,
            'method': 'fallback_center_crop',
            'coverage_before': 0.0,
            'coverage_after': 0.0,
            'adjustments_made': 0,
            'mouth_width_px': 0,
            'crop_w_px': crop_size,
            'target_ratio': 0.0
        }

        return resized_crop, metrics
    
    def segment_lips_with_sam(self, frame: np.ndarray, face_bbox: Tuple[int, int, int, int]) -> Optional[np.ndarray]:
        """Segment lips using SAM within face bounding box"""
        if self.sam_predictor is None:
            return None

        x1, y1, x2, y2 = face_bbox

        # Extract face region
        face_crop = frame[y1:y2, x1:x2]

        if face_crop.size == 0:
            return None

        try:
            # Set image for SAM
            self.sam_predictor.set_image(face_crop)

            # Create prompt point in lower center of face (approximate lip location)
            face_h, face_w = face_crop.shape[:2]
            lip_point = np.array([[face_w // 2, int(face_h * 0.75)]])  # Lower center
            lip_label = np.array([1])

            # Generate mask
            masks, scores, _ = self.sam_predictor.predict(
                point_coords=lip_point,
                point_labels=lip_label,
                multimask_output=True
            )

            if len(masks) == 0:
                return None

            # Select best mask
            best_mask = masks[np.argmax(scores)]

            # Apply mask to face crop
            masked_face = face_crop.copy()
            masked_face[~best_mask] = 0  # Set non-lip regions to black

            return masked_face

        except Exception as e:
            self.logger.debug(f"SAM segmentation failed: {e}")
            return None
    
    def standardize_clip(self, frames: np.ndarray) -> Optional[np.ndarray]:
        """Standardize clip to fixed size and frame count"""
        if len(frames) == 0:
            return None
        
        # Temporal standardization: sample to target frame count
        current_frames = len(frames)
        
        if current_frames < self.frame_length:
            # Pad by repeating last frame
            padding_needed = self.frame_length - current_frames
            last_frame = frames[-1:].repeat(padding_needed, axis=0)
            frames = np.concatenate([frames, last_frame], axis=0)
        elif current_frames > self.frame_length:
            # Uniform sampling
            indices = np.linspace(0, current_frames - 1, self.frame_length, dtype=int)
            frames = frames[indices]
        
        # Spatial standardization: resize to target size
        standardized_frames = []
        for frame in frames:
            if frame.shape[:2] != (self.img_size, self.img_size):
                resized = cv2.resize(frame, (self.img_size, self.img_size))
                standardized_frames.append(resized)
            else:
                standardized_frames.append(frame)
        
        standardized_frames = np.array(standardized_frames)
        
        # Pixel normalization: scale to [0, 1]
        standardized_frames = standardized_frames.astype(np.float32) / 255.0
        
        return standardized_frames
    


    def process_single_video(self, s3_key: str, output_name: str) -> Dict[str, any]:
        """Process a single video through the complete pipeline"""
        self.quality_stats['total_processed'] += 1

        # Download video to temporary file
        with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as tmp_file:
            temp_path = tmp_file.name

        try:
            # Download from S3
            if not self.s3_manager.download_video(s3_key, temp_path):
                self.quality_stats['processing_error'] += 1
                return {'success': False, 'error': 'download_failed'}

            # Extract frames
            frames = self.extract_frames_from_video(temp_path)
            if frames is None:
                self.quality_stats['processing_error'] += 1
                return {'success': False, 'error': 'frame_extraction_failed'}

            # Process frames using landmark-anchored, scale-controlled cropping
            processed_frames, validation_results, clip_metrics = self.extract_landmark_anchored_lip_crop(frames)

            if not processed_frames:
                self.quality_stats['processing_error'] += 1
                return {'success': False, 'error': 'no_frames_processed'}

            # Convert to numpy array - all frames should now have the same shape
            try:
                processed_frames = np.array(processed_frames)
            except ValueError as e:
                self.quality_stats['processing_error'] += 1
                return {'success': False, 'error': f'frame_array_creation_failed: {e}'}

            # Auto-scoring system using landmark-anchored metrics
            frames_with_landmarks = sum(1 for r in validation_results if r['has_landmarks'])
            valid_frames = sum(1 for r in validation_results if r['valid'])
            total_frames = len(validation_results)

            # Use clip-level metrics from landmark-anchored processing
            frames_with_landmarks_pct = clip_metrics['frames_with_landmarks_pct']
            is_high_quality = clip_metrics['valid_clip']
            avg_coverage_after = clip_metrics['avg_coverage_after']
            avg_mouth_width = clip_metrics['avg_mouth_width']
            avg_confidence = np.mean([r['confidence'] for r in validation_results]) if validation_results else 0

            if not is_high_quality:
                # Mark as low quality but still process (for logging)
                self.quality_stats['invalid_crop'] += 1
                error_reason = 'low_landmark_coverage'

                # Still process the clip but mark as low quality
                quality_info = {
                    'success': False,
                    'error': error_reason,
                    'frames_with_landmarks_pct': frames_with_landmarks_pct,
                    'avg_coverage_after': avg_coverage_after,
                    'avg_mouth_width': avg_mouth_width,
                    'avg_confidence': avg_confidence,
                    'valid_frames': valid_frames,
                    'total_frames': total_frames,
                    'is_high_quality': False,
                    'clip_metrics': clip_metrics
                }
            else:
                # High quality clip (≥90% landmark coverage)
                quality_info = {
                    'success': True,
                    'frames_with_landmarks_pct': frames_with_landmarks_pct,
                    'avg_coverage_after': avg_coverage_after,
                    'avg_mouth_width': avg_mouth_width,
                    'avg_confidence': avg_confidence,
                    'valid_frames': valid_frames,
                    'total_frames': total_frames,
                    'is_high_quality': True,
                    'clip_metrics': clip_metrics
                }

            # Standardize clip
            standardized_clip = self.standardize_clip(processed_frames)
            if standardized_clip is None:
                self.quality_stats['processing_error'] += 1
                return {'success': False, 'error': 'standardization_failed'}

            # Save processed clip
            output_path = os.path.join(self.processed_data_dir, 'clips', f"{output_name}.npy")
            save_frames_as_npy(standardized_clip, output_path)

            self.quality_stats['successful'] += 1

            # Update quality info with processing details
            quality_info.update({
                'output_path': output_path,
                'original_frames': len(frames),
                'processed_frames': len(standardized_clip),
                'has_landmarks': frames_with_landmarks > 0
            })

            return quality_info

        except Exception as e:
            self.logger.error(f"❌ Error processing {s3_key}: {e}")
            self.quality_stats['processing_error'] += 1
            return {'success': False, 'error': str(e)}

        finally:
            # Clean up temporary file
            if os.path.exists(temp_path):
                os.unlink(temp_path)

    def process_from_manifest(self, manifest_path: str, limit_per_class: Optional[int] = None):
        """Process all videos listed in a manifest file"""
        self.logger.info(f"📋 Processing videos from manifest: {manifest_path}")

        # Load manifest
        df = pd.read_csv(manifest_path)

        # Apply limit per class if specified
        if limit_per_class is not None:
            self.logger.info(f"🔢 Limiting to {limit_per_class} samples per class")
            limited_dfs = []
            for phrase in df['phrase'].unique():
                phrase_df = df[df['phrase'] == phrase].head(limit_per_class)
                limited_dfs.append(phrase_df)
            df = pd.concat(limited_dfs, ignore_index=True)
            self.logger.info(f"📊 Limited dataset size: {len(df)} samples")

        # Process each video
        results = []

        for idx, row in tqdm(df.iterrows(), total=len(df), desc="Processing videos"):
            s3_key = row['s3_key']
            phrase = row['phrase']
            speaker_id = row['speaker_id']

            # Generate output name
            output_name = f"{phrase}_{speaker_id}_{idx:06d}"

            # Process video
            result = self.process_single_video(s3_key, output_name)

            # Add metadata
            result.update({
                'manifest_idx': idx,
                's3_key': s3_key,
                'phrase': phrase,
                'speaker_id': speaker_id,
                'output_name': output_name
            })

            # Record quality data for CSV (landmark-anchored approach)
            if 'frames_with_landmarks_pct' in result:
                # Extract detailed metrics from clip_metrics if available
                clip_metrics = result.get('clip_metrics', {})
                frame_metrics = clip_metrics.get('frame_metrics', [])

                # Calculate average metrics across frames
                avg_mouth_width = np.mean([m.get('mouth_width_px', 0) for m in frame_metrics if m.get('mouth_width_px', 0) > 0]) if frame_metrics else 0
                avg_crop_w = np.mean([m.get('crop_w_px', 0) for m in frame_metrics if m.get('crop_w_px', 0) > 0]) if frame_metrics else 0
                avg_target_ratio = np.mean([m.get('target_ratio', 0) for m in frame_metrics if m.get('target_ratio', 0) > 0]) if frame_metrics else 0
                avg_coverage_before = np.mean([m.get('coverage_before', 0) for m in frame_metrics]) if frame_metrics else 0
                avg_coverage_after = np.mean([m.get('coverage_after', 0) for m in frame_metrics]) if frame_metrics else 0
                total_adjustments = sum([m.get('adjustments_made', 0) for m in frame_metrics]) if frame_metrics else 0

                quality_record = {
                    'clip_id': output_name,
                    'phrase': phrase,
                    'speaker_id': speaker_id,
                    'valid': result.get('is_high_quality', False),
                    'frames_with_landmarks_pct': result.get('frames_with_landmarks_pct', 0),
                    'mouth_width_px': avg_mouth_width,
                    'crop_w_px': avg_crop_w,
                    'target_ratio': avg_target_ratio,
                    'coverage_before': avg_coverage_before,
                    'coverage_after': avg_coverage_after,
                    'adjustments_made': total_adjustments,
                    'avg_confidence': result.get('avg_confidence', 0),
                    'valid_frames': result.get('valid_frames', 0),
                    'total_frames': result.get('total_frames', 0),
                    'success': result.get('success', False),
                    'error': result.get('error', '')
                }
                self.quality_records.append(quality_record)

            results.append(result)

        # Save processing log
        log_name = os.path.basename(manifest_path).replace('.csv', '_processing_log.csv')
        log_path = os.path.join(self.processed_data_dir, 'quality_logs', log_name)

        results_df = pd.DataFrame(results)
        results_df.to_csv(log_path, index=False)

        # Save quality CSV with auto-scoring results
        quality_csv_path = os.path.join(self.processed_data_dir, 'quality_logs', 'quality_scores.csv')
        self._save_quality_csv(quality_csv_path)

        # Print statistics
        self._print_processing_stats()

        self.logger.info(f"✅ Processing completed. Log saved to: {log_path}")
        self.logger.info(f"📊 Quality scores saved to: {quality_csv_path}")

    def _save_quality_csv(self, output_path="data_processed/quality_logs/quality_scores.csv"):
        """Save quality records to CSV"""
        if not self.quality_records:
            return

        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        import pandas as pd
        df = pd.DataFrame(self.quality_records)
        df.to_csv(output_path, index=False)

        self.logger.info(f"💾 Quality scores saved to: {output_path}")

        # Print summary statistics
        high_quality_count = len(df[df['valid_crop'] == True])
        total_count = len(df)
        avg_landmark_coverage = df['landmark_coverage_pct'].mean()

        print(f"\n📊 Quality Summary:")
        print("=" * 50)
        print(f"Total clips processed: {total_count}")
        print(f"High quality (≥80%):   {high_quality_count} ({high_quality_count/total_count*100:.1f}%)")
        print(f"Low quality (<80%):    {total_count-high_quality_count} ({(total_count-high_quality_count)/total_count*100:.1f}%)")
        print(f"Avg landmark coverage: {avg_landmark_coverage:.1f}%")
        print("=" * 50)

    def _print_processing_stats(self):
        """Print processing statistics"""
        total = self.quality_stats['total_processed']

        print(f"\n📊 Processing Statistics")
        print("=" * 50)
        print(f"Total processed:     {total:6d}")
        print(f"Successful:          {self.quality_stats['successful']:6d} ({self.quality_stats['successful']/total*100:.1f}%)")
        print(f"Invalid crops:       {self.quality_stats['invalid_crop']:6d} ({self.quality_stats['invalid_crop']/total*100:.1f}%)")
        print(f"No landmarks:        {self.quality_stats['no_landmarks']:6d} ({self.quality_stats['no_landmarks']/total*100:.1f}%)")
        print(f"Low confidence:      {self.quality_stats['low_confidence']:6d} ({self.quality_stats['low_confidence']/total*100:.1f}%)")
        print(f"Processing errors:   {self.quality_stats['processing_error']:6d} ({self.quality_stats['processing_error']/total*100:.1f}%)")
        print("=" * 50)


def main():
    parser = argparse.ArgumentParser(description='Preprocess videos using YOLO→SAM pipeline')
    parser.add_argument('--config', default='configs/training.yaml', help='Config file path')
    parser.add_argument('--manifest', required=True, help='Manifest CSV file to process')
    parser.add_argument('--limit_per_class', type=int, help='Limit number of samples per class (for testing)')
    args = parser.parse_args()

    # Load configuration
    config = load_config(args.config)

    # Set seed for reproducibility
    set_seed(config.get('seed', 42))

    # Process videos
    preprocessor = VideoPreprocessor(config)
    preprocessor.process_from_manifest(args.manifest, limit_per_class=args.limit_per_class)


if __name__ == "__main__":
    main()
