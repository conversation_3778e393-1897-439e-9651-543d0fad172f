"""
Preprocessing Pipeline for ICU Lipreading
YOLO Face Detection → SAM Lip Segmentation → Standardization
"""

import os
import cv2
import numpy as np
import pandas as pd
import tempfile
from pathlib import Path
from typing import List, Dict, Tuple, Optional
import argparse
from tqdm import tqdm
import torch
from ultralytics import YOLO
from segment_anything import SamPredictor, sam_model_registry

from utils import (
    setup_logging, set_seed, load_config, S3Manager,
    create_directories, get_video_info, calculate_motion_score,
    calculate_blur_score, save_frames_as_npy
)


class VideoPreprocessor:
    """Preprocess videos using YOLO→SAM pipeline"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.logger = setup_logging(
            config.get('logging', {}).get('level', 'INFO'),
            config.get('logging', {}).get('log_dir', './logs')
        )
        
        # Configuration
        self.img_size = config['data']['img_size']
        self.frame_length = config['data']['frames_per_clip']
        self.fps_target = config['data']['fps_target']
        self.processed_data_dir = config['data']['data_processed_dir']
        
        # Quality thresholds
        self.min_face_area = config['segmentation']['quality']['min_face_area']
        self.min_motion_threshold = config['segmentation']['quality']['min_motion_threshold']
        self.max_blur_threshold = config['segmentation']['quality']['max_blur_threshold']
        
        # Initialize S3 manager
        self.s3_manager = S3Manager(config['data']['s3_bucket'])
        
        # Create output directories
        create_directories([
            self.processed_data_dir,
            os.path.join(self.processed_data_dir, 'clips'),
            os.path.join(self.processed_data_dir, 'quality_logs')
        ])
        
        # Initialize models
        self._load_models()
        
        # Quality tracking
        self.quality_stats = {
            'total_processed': 0,
            'successful': 0,
            'no_face_detected': 0,
            'face_too_small': 0,
            'low_motion': 0,
            'too_blurry': 0,
            'processing_error': 0
        }
    
    def _load_models(self):
        """Load YOLO and SAM models"""
        self.logger.info("🤖 Loading YOLO and SAM models...")
        
        # Load YOLO
        yolo_model = self.config['segmentation']['yolo']['model']
        self.yolo = YOLO(yolo_model)
        self.yolo_conf = self.config['segmentation']['yolo']['conf_threshold']
        
        # Load SAM (skip if PyTorch 2.6 compatibility issues)
        sam_type = self.config['segmentation']['sam']['model_type']
        sam_checkpoint = self.config['segmentation']['sam']['checkpoint_path']

        if not os.path.exists(sam_checkpoint):
            self.logger.warning(f"⚠️ SAM checkpoint not found: {sam_checkpoint}")
            self.sam_predictor = None
        else:
            try:
                device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
                from utils import load_sam_model
                self.sam_predictor = load_sam_model(sam_type, sam_checkpoint, device)
                self.logger.info("✅ SAM model loaded")
            except Exception as e:
                self.logger.warning(f"⚠️ SAM loading failed: {e}")
                self.logger.info("📝 Continuing with YOLO-only mode")
                self.sam_predictor = None
        
        self.logger.info("✅ Models loaded successfully")
    
    def extract_frames_from_video(self, video_path: str) -> Optional[np.ndarray]:
        """Extract frames from video file"""
        cap = cv2.VideoCapture(video_path)

        if not cap.isOpened():
            self.logger.error(f"❌ Cannot open video: {video_path}")
            return None

        frames = []
        frame_count = 0
        max_frames = 200  # Limit to prevent memory issues

        while frame_count < max_frames:
            ret, frame = cap.read()
            if not ret:
                break

            # Ensure frame has consistent shape
            if frame is None or frame.size == 0:
                continue

            # Convert BGR to RGB
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

            # Ensure all frames have the same shape
            if len(frames) > 0 and frame_rgb.shape != frames[0].shape:
                # Resize to match first frame
                frame_rgb = cv2.resize(frame_rgb, (frames[0].shape[1], frames[0].shape[0]))

            frames.append(frame_rgb)
            frame_count += 1

        cap.release()

        if not frames:
            return None

        try:
            return np.array(frames)
        except ValueError as e:
            self.logger.error(f"❌ Error creating frame array: {e}")
            return None
    
    def detect_face_with_yolo(self, frame: np.ndarray) -> Optional[Tuple[int, int, int, int]]:
        """Detect face using YOLO and return bounding box"""
        results = self.yolo(frame, conf=self.yolo_conf, classes=[0], verbose=False)  # Person class
        
        if not results or not results[0].boxes:
            return None
        
        # Get the largest detection (assuming it's the main person)
        boxes = results[0].boxes.xyxy.cpu().numpy()
        areas = [(box[2] - box[0]) * (box[3] - box[1]) for box in boxes]
        
        if not areas:
            return None
        
        largest_idx = np.argmax(areas)
        box = boxes[largest_idx]
        
        # Focus on upper half for face region
        x1, y1, x2, y2 = map(int, box)
        face_height = y2 - y1
        face_y2 = y1 + int(face_height * 0.6)  # Upper 60% for face
        
        return (x1, y1, x2, face_y2)
    
    def segment_lips_with_sam(self, frame: np.ndarray, face_bbox: Tuple[int, int, int, int]) -> Optional[np.ndarray]:
        """Segment lips using SAM within face bounding box"""
        if self.sam_predictor is None:
            return None

        x1, y1, x2, y2 = face_bbox

        # Extract face region
        face_crop = frame[y1:y2, x1:x2]

        if face_crop.size == 0:
            return None

        try:
            # Set image for SAM
            self.sam_predictor.set_image(face_crop)

            # Create prompt point in lower center of face (approximate lip location)
            face_h, face_w = face_crop.shape[:2]
            lip_point = np.array([[face_w // 2, int(face_h * 0.75)]])  # Lower center
            lip_label = np.array([1])

            # Generate mask
            masks, scores, _ = self.sam_predictor.predict(
                point_coords=lip_point,
                point_labels=lip_label,
                multimask_output=True
            )

            if len(masks) == 0:
                return None

            # Select best mask
            best_mask = masks[np.argmax(scores)]

            # Apply mask to face crop
            masked_face = face_crop.copy()
            masked_face[~best_mask] = 0  # Set non-lip regions to black

            return masked_face

        except Exception as e:
            self.logger.debug(f"SAM segmentation failed: {e}")
            return None
    
    def standardize_clip(self, frames: np.ndarray) -> Optional[np.ndarray]:
        """Standardize clip to fixed size and frame count"""
        if len(frames) == 0:
            return None
        
        # Temporal standardization: sample to target frame count
        current_frames = len(frames)
        
        if current_frames < self.frame_length:
            # Pad by repeating last frame
            padding_needed = self.frame_length - current_frames
            last_frame = frames[-1:].repeat(padding_needed, axis=0)
            frames = np.concatenate([frames, last_frame], axis=0)
        elif current_frames > self.frame_length:
            # Uniform sampling
            indices = np.linspace(0, current_frames - 1, self.frame_length, dtype=int)
            frames = frames[indices]
        
        # Spatial standardization: resize to target size
        standardized_frames = []
        for frame in frames:
            if frame.shape[:2] != (self.img_size, self.img_size):
                resized = cv2.resize(frame, (self.img_size, self.img_size))
                standardized_frames.append(resized)
            else:
                standardized_frames.append(frame)
        
        standardized_frames = np.array(standardized_frames)
        
        # Pixel normalization: scale to [0, 1]
        standardized_frames = standardized_frames.astype(np.float32) / 255.0
        
        return standardized_frames
    
    def assess_quality(self, frames: np.ndarray, face_bbox: Optional[Tuple]) -> Dict[str, bool]:
        """Assess clip quality and return flags"""
        flags = {
            'no_detection': face_bbox is None,
            'face_too_small': False,
            'low_motion': False,
            'too_blurry': False
        }
        
        if face_bbox is not None:
            # Check face size
            x1, y1, x2, y2 = face_bbox
            face_area = (x2 - x1) * (y2 - y1)
            flags['face_too_small'] = face_area < self.min_face_area
            
            # Check motion
            motion_score = calculate_motion_score(frames)
            flags['low_motion'] = motion_score < self.min_motion_threshold
            
            # Check blur (use middle frame)
            if len(frames) > 0:
                mid_frame = frames[len(frames) // 2]
                blur_score = calculate_blur_score(mid_frame)
                flags['too_blurry'] = blur_score < self.max_blur_threshold
        
        return flags

    def process_single_video(self, s3_key: str, output_name: str) -> Dict[str, any]:
        """Process a single video through the complete pipeline"""
        self.quality_stats['total_processed'] += 1

        # Download video to temporary file
        with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as tmp_file:
            temp_path = tmp_file.name

        try:
            # Download from S3
            if not self.s3_manager.download_video(s3_key, temp_path):
                self.quality_stats['processing_error'] += 1
                return {'success': False, 'error': 'download_failed'}

            # Extract frames
            frames = self.extract_frames_from_video(temp_path)
            if frames is None:
                self.quality_stats['processing_error'] += 1
                return {'success': False, 'error': 'frame_extraction_failed'}

            # Process each frame through YOLO→SAM pipeline
            processed_frames = []
            face_bboxes = []
            target_size = (self.img_size, self.img_size)  # Standard size for all crops

            for frame in frames:
                # YOLO face detection
                face_bbox = self.detect_face_with_yolo(frame)
                face_bboxes.append(face_bbox)

                if face_bbox is None:
                    # Use center crop of original frame if no face detected
                    h, w = frame.shape[:2]
                    crop_size = min(h, w)
                    start_h = (h - crop_size) // 2
                    start_w = (w - crop_size) // 2
                    center_crop = frame[start_h:start_h+crop_size, start_w:start_w+crop_size]
                    # Resize to target size
                    resized_crop = cv2.resize(center_crop, target_size)
                    processed_frames.append(resized_crop)
                    continue

                # SAM lip segmentation
                lip_region = self.segment_lips_with_sam(frame, face_bbox)

                if lip_region is not None:
                    # Resize lip region to target size
                    resized_lip = cv2.resize(lip_region, target_size)
                    processed_frames.append(resized_lip)
                else:
                    # Fallback to face crop if SAM fails
                    x1, y1, x2, y2 = face_bbox
                    face_crop = frame[y1:y2, x1:x2]
                    if face_crop.size > 0:
                        # Resize face crop to target size
                        resized_face = cv2.resize(face_crop, target_size)
                        processed_frames.append(resized_face)
                    else:
                        # Use center crop as final fallback
                        h, w = frame.shape[:2]
                        crop_size = min(h, w)
                        start_h = (h - crop_size) // 2
                        start_w = (w - crop_size) // 2
                        center_crop = frame[start_h:start_h+crop_size, start_w:start_w+crop_size]
                        resized_crop = cv2.resize(center_crop, target_size)
                        processed_frames.append(resized_crop)

            if not processed_frames:
                self.quality_stats['processing_error'] += 1
                return {'success': False, 'error': 'no_frames_processed'}

            # Convert to numpy array - all frames should now have the same shape
            try:
                processed_frames = np.array(processed_frames)
            except ValueError as e:
                self.quality_stats['processing_error'] += 1
                return {'success': False, 'error': f'frame_array_creation_failed: {e}'}

            # Quality assessment
            representative_bbox = next((bbox for bbox in face_bboxes if bbox is not None), None)
            quality_flags = self.assess_quality(processed_frames, representative_bbox)

            # Check if clip passes quality filters
            if quality_flags['no_detection']:
                self.quality_stats['no_face_detected'] += 1
                return {'success': False, 'error': 'no_face_detected', 'flags': quality_flags}

            if quality_flags['face_too_small']:
                self.quality_stats['face_too_small'] += 1
                return {'success': False, 'error': 'face_too_small', 'flags': quality_flags}

            if quality_flags['low_motion']:
                self.quality_stats['low_motion'] += 1
                return {'success': False, 'error': 'low_motion', 'flags': quality_flags}

            if quality_flags['too_blurry']:
                self.quality_stats['too_blurry'] += 1
                return {'success': False, 'error': 'too_blurry', 'flags': quality_flags}

            # Standardize clip
            standardized_clip = self.standardize_clip(processed_frames)
            if standardized_clip is None:
                self.quality_stats['processing_error'] += 1
                return {'success': False, 'error': 'standardization_failed'}

            # Save processed clip
            output_path = os.path.join(self.processed_data_dir, 'clips', f"{output_name}.npy")
            save_frames_as_npy(standardized_clip, output_path)

            self.quality_stats['successful'] += 1

            return {
                'success': True,
                'output_path': output_path,
                'original_frames': len(frames),
                'processed_frames': len(standardized_clip),
                'flags': quality_flags
            }

        except Exception as e:
            self.logger.error(f"❌ Error processing {s3_key}: {e}")
            self.quality_stats['processing_error'] += 1
            return {'success': False, 'error': str(e)}

        finally:
            # Clean up temporary file
            if os.path.exists(temp_path):
                os.unlink(temp_path)

    def process_from_manifest(self, manifest_path: str, limit_per_class: Optional[int] = None):
        """Process all videos listed in a manifest file"""
        self.logger.info(f"📋 Processing videos from manifest: {manifest_path}")

        # Load manifest
        df = pd.read_csv(manifest_path)

        # Apply limit per class if specified
        if limit_per_class is not None:
            self.logger.info(f"🔢 Limiting to {limit_per_class} samples per class")
            limited_dfs = []
            for phrase in df['phrase'].unique():
                phrase_df = df[df['phrase'] == phrase].head(limit_per_class)
                limited_dfs.append(phrase_df)
            df = pd.concat(limited_dfs, ignore_index=True)
            self.logger.info(f"📊 Limited dataset size: {len(df)} samples")

        # Process each video
        results = []

        for idx, row in tqdm(df.iterrows(), total=len(df), desc="Processing videos"):
            s3_key = row['s3_key']
            phrase = row['phrase']
            speaker_id = row['speaker_id']

            # Generate output name
            output_name = f"{phrase}_{speaker_id}_{idx:06d}"

            # Process video
            result = self.process_single_video(s3_key, output_name)

            # Add metadata
            result.update({
                'manifest_idx': idx,
                's3_key': s3_key,
                'phrase': phrase,
                'speaker_id': speaker_id,
                'output_name': output_name
            })

            results.append(result)

        # Save processing log
        log_name = os.path.basename(manifest_path).replace('.csv', '_processing_log.csv')
        log_path = os.path.join(self.processed_data_dir, 'quality_logs', log_name)

        results_df = pd.DataFrame(results)
        results_df.to_csv(log_path, index=False)

        # Print statistics
        self._print_processing_stats()

        self.logger.info(f"✅ Processing completed. Log saved to: {log_path}")

    def _print_processing_stats(self):
        """Print processing statistics"""
        total = self.quality_stats['total_processed']

        print(f"\n📊 Processing Statistics")
        print("=" * 50)
        print(f"Total processed:     {total:6d}")
        print(f"Successful:          {self.quality_stats['successful']:6d} ({self.quality_stats['successful']/total*100:.1f}%)")
        print(f"No face detected:    {self.quality_stats['no_face_detected']:6d} ({self.quality_stats['no_face_detected']/total*100:.1f}%)")
        print(f"Face too small:      {self.quality_stats['face_too_small']:6d} ({self.quality_stats['face_too_small']/total*100:.1f}%)")
        print(f"Low motion:          {self.quality_stats['low_motion']:6d} ({self.quality_stats['low_motion']/total*100:.1f}%)")
        print(f"Too blurry:          {self.quality_stats['too_blurry']:6d} ({self.quality_stats['too_blurry']/total*100:.1f}%)")
        print(f"Processing errors:   {self.quality_stats['processing_error']:6d} ({self.quality_stats['processing_error']/total*100:.1f}%)")
        print("=" * 50)


def main():
    parser = argparse.ArgumentParser(description='Preprocess videos using YOLO→SAM pipeline')
    parser.add_argument('--config', default='configs/training.yaml', help='Config file path')
    parser.add_argument('--manifest', required=True, help='Manifest CSV file to process')
    parser.add_argument('--limit_per_class', type=int, help='Limit number of samples per class (for testing)')
    args = parser.parse_args()

    # Load configuration
    config = load_config(args.config)

    # Set seed for reproducibility
    set_seed(config.get('seed', 42))

    # Process videos
    preprocessor = VideoPreprocessor(config)
    preprocessor.process_from_manifest(args.manifest, limit_per_class=args.limit_per_class)


if __name__ == "__main__":
    main()
