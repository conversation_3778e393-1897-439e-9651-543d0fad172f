"""
Preprocessing Pipeline for ICU Lipreading
YOLO Face Detection → SAM Lip Segmentation → Standardization
"""

import os
import cv2
import numpy as np
import pandas as pd
import tempfile
from pathlib import Path
from typing import List, Dict, Tuple, Optional
import argparse
from tqdm import tqdm
import torch
from ultralytics import YOLO
from segment_anything import SamPredictor, sam_model_registry

from utils import (
    setup_logging, set_seed, load_config, S3Manager,
    create_directories, get_video_info, calculate_motion_score,
    calculate_blur_score, save_frames_as_npy
)


class VideoPreprocessor:
    """Preprocess videos using YOLO→SAM pipeline"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.logger = setup_logging(
            config.get('logging', {}).get('level', 'INFO'),
            config.get('logging', {}).get('log_dir', './logs')
        )
        
        # Configuration
        self.img_size = config['data']['img_size']
        self.frame_length = config['data']['frames_per_clip']
        self.fps_target = config['data']['fps_target']
        self.processed_data_dir = config['data']['data_processed_dir']
        
        # Quality thresholds
        self.min_face_area = config['segmentation']['quality']['min_face_area']
        self.min_motion_threshold = config['segmentation']['quality']['min_motion_threshold']
        self.max_blur_threshold = config['segmentation']['quality']['max_blur_threshold']
        
        # Initialize S3 manager
        self.s3_manager = S3Manager(config['data']['s3_bucket'])
        
        # Create output directories
        create_directories([
            self.processed_data_dir,
            os.path.join(self.processed_data_dir, 'clips'),
            os.path.join(self.processed_data_dir, 'quality_logs')
        ])
        
        # Initialize models
        self._load_models()

        # Quality tracking
        self.quality_stats = {
            'total_processed': 0,
            'successful': 0,
            'invalid_crop': 0,
            'no_landmarks': 0,
            'low_confidence': 0,
            'processing_error': 0
        }
    
    def _load_models(self):
        """Load YOLO, SAM, and MediaPipe models"""
        self.logger.info("🤖 Loading models...")

        # Load YOLO (optional for quality assessment)
        try:
            yolo_model = self.config['segmentation']['yolo']['model']
            self.yolo = YOLO(yolo_model)
            self.yolo_conf = self.config['segmentation']['yolo']['conf_threshold']
            self.logger.info("✅ YOLO loaded")
        except Exception as e:
            self.logger.warning(f"⚠️ YOLO loading failed: {e}")
            self.yolo = None

        # Load SAM (optional, skip if PyTorch 2.6 compatibility issues)
        sam_type = self.config['segmentation']['sam']['model_type']
        sam_checkpoint = self.config['segmentation']['sam']['checkpoint_path']

        if not os.path.exists(sam_checkpoint):
            self.logger.warning(f"⚠️ SAM checkpoint not found: {sam_checkpoint}")
            self.sam_predictor = None
        else:
            try:
                device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
                from utils import load_sam_model
                self.sam_predictor = load_sam_model(sam_type, sam_checkpoint, device)
                self.logger.info("✅ SAM model loaded")
            except Exception as e:
                self.logger.warning(f"⚠️ SAM loading failed: {e}")
                self.sam_predictor = None

        # Load MediaPipe FaceMesh
        try:
            import mediapipe as mp
            self.mp_face_mesh = mp.solutions.face_mesh
            self.face_mesh = self.mp_face_mesh.FaceMesh(
                static_image_mode=True,
                max_num_faces=1,
                refine_landmarks=True,
                min_detection_confidence=0.5,
                min_tracking_confidence=0.5
            )
            self.logger.info("✅ MediaPipe FaceMesh loaded")
            self.mediapipe_available = True
        except ImportError:
            self.logger.warning("⚠️ MediaPipe not available - install with Python 3.11")
            self.face_mesh = None
            self.mediapipe_available = False
        except Exception as e:
            self.logger.warning(f"⚠️ MediaPipe loading failed: {e}")
            self.face_mesh = None
            self.mediapipe_available = False

        self.logger.info("✅ Models loaded successfully")

        # Initialize quality tracking CSV
        self.quality_records = []
    
    def extract_frames_from_video(self, video_path: str) -> Optional[np.ndarray]:
        """Extract frames from video file"""
        cap = cv2.VideoCapture(video_path)

        if not cap.isOpened():
            self.logger.error(f"❌ Cannot open video: {video_path}")
            return None

        frames = []
        frame_count = 0
        max_frames = 200  # Limit to prevent memory issues

        while frame_count < max_frames:
            ret, frame = cap.read()
            if not ret:
                break

            # Ensure frame has consistent shape
            if frame is None or frame.size == 0:
                continue

            # Convert BGR to RGB
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

            # Ensure all frames have the same shape
            if len(frames) > 0 and frame_rgb.shape != frames[0].shape:
                # Resize to match first frame
                frame_rgb = cv2.resize(frame_rgb, (frames[0].shape[1], frames[0].shape[0]))

            frames.append(frame_rgb)
            frame_count += 1

        cap.release()

        if not frames:
            return None

        try:
            return np.array(frames)
        except ValueError as e:
            self.logger.error(f"❌ Error creating frame array: {e}")
            return None
    
    def extract_hybrid_lip_crop(self, frame: np.ndarray) -> tuple:
        """
        Extract lip crop using hybrid strategy:
        1. YOLO face detection for lower face bounding box
        2. 3x2 grid heuristic within face bbox
        3. MediaPipe validation and dynamic adjustment
        Returns (crop, is_valid, has_landmarks, confidence, adjustment_info, face_bbox)
        """
        h, w = frame.shape[:2]

        # Step 1: YOLO face detection for lower face bounding box
        face_bbox = None
        if self.yolo is not None:
            try:
                results = self.yolo(frame, conf=self.yolo_conf, classes=[0], verbose=False)
                if results and results[0].boxes:
                    boxes = results[0].boxes.xyxy.cpu().numpy()
                    areas = [(box[2] - box[0]) * (box[3] - box[1]) for box in boxes]
                    if areas:
                        largest_idx = np.argmax(areas)
                        box = boxes[largest_idx]
                        x1, y1, x2, y2 = map(int, box)

                        # Focus on lower 60% of face for lip region
                        face_height = y2 - y1
                        y1_lower = y1 + int(face_height * 0.4)  # Start from 40% down
                        face_bbox = (x1, y1_lower, x2, y2)
            except Exception as e:
                self.logger.debug(f"YOLO detection failed: {e}")

        # Step 2: Apply 3x2 grid heuristic within face bbox (or full frame if no face)
        if face_bbox:
            fx1, fy1, fx2, fy2 = face_bbox
            face_w = fx2 - fx1
            face_h = fy2 - fy1

            # 3x2 grid within face bbox
            col_width = face_w // 3
            row_height = face_h // 2

            # Top-middle cell within face bbox
            x1 = fx1 + col_width
            x2 = fx1 + 2 * col_width
            y1 = fy1
            y2 = fy1 + row_height
        else:
            # Fallback to full frame 3x2 grid
            col_width = w // 3
            row_height = h // 2

            x1 = col_width
            x2 = 2 * col_width
            y1 = 0
            y2 = row_height

        # Ensure coordinates are within frame bounds
        x1 = max(0, min(x1, w-1))
        x2 = max(x1+1, min(x2, w))
        y1 = max(0, min(y1, h-1))
        y2 = max(y1+1, min(y2, h))

        # Extract initial crop
        initial_crop = frame[y1:y2, x1:x2]

        # Step 3: MediaPipe validation and dynamic adjustment
        if self.mediapipe_available and self.face_mesh is not None:
            is_valid, confidence, has_landmarks = self._validate_crop_with_mediapipe(
                frame, x1, y1, x2, y2
            )

            adjustment_info = "original_crop"
            final_crop = initial_crop
            final_coords = (x1, y1, x2, y2)

            if not is_valid:
                # Try different adjustments
                best_crop = initial_crop
                best_confidence = confidence
                best_coords = (x1, y1, x2, y2)
                best_adjustment = "original_crop"

                # Try vertical shifts (up and down)
                crop_h = y2 - y1
                crop_w = x2 - x1

                for shift_factor in [-0.3, -0.15, 0.15, 0.3]:  # Try shifting up/down
                    shift_y = int(crop_h * shift_factor)
                    new_y1 = max(0, min(y1 + shift_y, h - crop_h))
                    new_y2 = new_y1 + crop_h

                    if new_y2 <= h:
                        test_valid, test_conf, test_landmarks = self._validate_crop_with_mediapipe(
                            frame, x1, new_y1, x2, new_y2
                        )

                        if test_valid and test_conf > best_confidence:
                            best_crop = frame[new_y1:new_y2, x1:x2]
                            best_confidence = test_conf
                            best_coords = (x1, new_y1, x2, new_y2)
                            best_adjustment = f"shifted_y_{shift_y}"

                # Try padding adjustments
                if not is_valid or best_confidence < 0.8:
                    for pad_factor in [0.1, 0.2, 0.3]:  # Try different padding amounts
                        pad_x = int(crop_w * pad_factor)
                        pad_y = int(crop_h * pad_factor)

                        new_x1 = max(0, x1 - pad_x)
                        new_x2 = min(w, x2 + pad_x)
                        new_y1 = max(0, y1 - pad_y)
                        new_y2 = min(h, y2 + pad_y)

                        test_valid, test_conf, test_landmarks = self._validate_crop_with_mediapipe(
                            frame, new_x1, new_y1, new_x2, new_y2
                        )

                        if test_valid and test_conf > best_confidence:
                            best_crop = frame[new_y1:new_y2, new_x1:new_x2]
                            best_confidence = test_conf
                            best_coords = (new_x1, new_y1, new_x2, new_y2)
                            best_adjustment = f"padded_{pad_factor:.1f}"

                final_crop = best_crop
                confidence = best_confidence
                adjustment_info = best_adjustment
                is_valid = confidence >= 0.7
        else:
            # MediaPipe not available - assume valid
            final_crop = initial_crop
            is_valid, confidence, has_landmarks = True, 1.0, True
            adjustment_info = "mediapipe_unavailable"

        # Resize to target size
        if final_crop.size > 0:
            resized_crop = cv2.resize(final_crop, (self.img_size, self.img_size))
        else:
            # Final fallback: center crop
            crop_size = min(h, w)
            start_h = (h - crop_size) // 2
            start_w = (w - crop_size) // 2
            center_crop = frame[start_h:start_h+crop_size, start_w:start_w+crop_size]
            resized_crop = cv2.resize(center_crop, (self.img_size, self.img_size))
            is_valid, confidence, has_landmarks = False, 0.0, False
            adjustment_info = "fallback_center_crop"

        return resized_crop, is_valid, has_landmarks, confidence, adjustment_info, face_bbox

    def _validate_crop_with_mediapipe(self, frame: np.ndarray, x1: int, y1: int, x2: int, y2: int) -> tuple:
        """
        Validate crop region using MediaPipe FaceMesh.
        Returns (is_valid, confidence, has_landmarks)
        """
        if not self.mediapipe_available or self.face_mesh is None:
            return True, 1.0, True

        try:
            # Extract crop region
            crop = frame[y1:y2, x1:x2]

            if crop.size == 0:
                return False, 0.0, False

            # Convert to RGB if needed
            if len(crop.shape) == 3 and crop.shape[2] == 3:
                rgb_crop = cv2.cvtColor(crop, cv2.COLOR_BGR2RGB)
            else:
                rgb_crop = crop

            # Convert to uint8 if needed
            if rgb_crop.dtype == np.float32:
                rgb_crop = (rgb_crop * 255).astype(np.uint8)

            # Run MediaPipe face detection
            results = self.face_mesh.process(rgb_crop)

            if not results.multi_face_landmarks:
                return False, 0.0, False

            # Get the first face
            face_landmarks = results.multi_face_landmarks[0]

            # Define comprehensive lip landmark indices (MediaPipe 468-point model)
            lip_landmark_indices = [
                # Upper lip outer boundary
                61, 84, 17, 314, 405, 320, 307, 375, 321, 308, 324, 318,
                # Lower lip outer boundary
                78, 95, 88, 178, 87, 14, 317, 402, 318, 324,
                # Mouth corners (critical for centering)
                61, 291,
                # Additional key lip points
                13, 82, 81, 80, 78, 95, 88, 178, 87, 14, 317, 402, 318, 324,
                # Inner lip contour
                78, 191, 80, 81, 82, 13, 312, 311, 310, 415, 308, 324, 318
            ]

            # Remove duplicates and ensure valid indices
            lip_landmark_indices = list(set([idx for idx in lip_landmark_indices if idx < 468]))

            # Count valid lip landmarks within crop bounds
            valid_lip_landmarks = 0
            total_lip_landmarks = len(lip_landmark_indices)

            h, w = rgb_crop.shape[:2]
            margin = 3  # Small margin to ensure landmarks are well within bounds

            lip_points = []
            for idx in lip_landmark_indices:
                if idx < len(face_landmarks.landmark):
                    landmark = face_landmarks.landmark[idx]
                    x = landmark.x * w
                    y = landmark.y * h
                    lip_points.append((x, y))

                    # Check if landmark is within crop bounds with margin
                    if margin <= x <= w - margin and margin <= y <= h - margin:
                        valid_lip_landmarks += 1

            # Calculate confidence based on landmark coverage
            confidence = valid_lip_landmarks / total_lip_landmarks if total_lip_landmarks > 0 else 0.0

            # Additional quality checks
            if len(lip_points) >= 4:
                # Check if lips are reasonably centered in the crop
                lip_points = np.array(lip_points)
                center_x, center_y = np.mean(lip_points, axis=0)

                # Penalize if lip center is too far from crop center
                crop_center_x, crop_center_y = w / 2, h / 2
                center_distance = np.sqrt((center_x - crop_center_x)**2 + (center_y - crop_center_y)**2)
                max_distance = min(w, h) * 0.3  # Allow 30% deviation from center

                centering_penalty = min(1.0, center_distance / max_distance)
                confidence *= (1.0 - centering_penalty * 0.3)  # Up to 30% penalty for poor centering

            # Consider valid if confidence >= 70%
            is_valid = confidence >= 0.7

            return is_valid, confidence, True

        except Exception as e:
            self.logger.debug(f"MediaPipe validation failed: {e}")
            return False, 0.0, False
    
    def segment_lips_with_sam(self, frame: np.ndarray, face_bbox: Tuple[int, int, int, int]) -> Optional[np.ndarray]:
        """Segment lips using SAM within face bounding box"""
        if self.sam_predictor is None:
            return None

        x1, y1, x2, y2 = face_bbox

        # Extract face region
        face_crop = frame[y1:y2, x1:x2]

        if face_crop.size == 0:
            return None

        try:
            # Set image for SAM
            self.sam_predictor.set_image(face_crop)

            # Create prompt point in lower center of face (approximate lip location)
            face_h, face_w = face_crop.shape[:2]
            lip_point = np.array([[face_w // 2, int(face_h * 0.75)]])  # Lower center
            lip_label = np.array([1])

            # Generate mask
            masks, scores, _ = self.sam_predictor.predict(
                point_coords=lip_point,
                point_labels=lip_label,
                multimask_output=True
            )

            if len(masks) == 0:
                return None

            # Select best mask
            best_mask = masks[np.argmax(scores)]

            # Apply mask to face crop
            masked_face = face_crop.copy()
            masked_face[~best_mask] = 0  # Set non-lip regions to black

            return masked_face

        except Exception as e:
            self.logger.debug(f"SAM segmentation failed: {e}")
            return None
    
    def standardize_clip(self, frames: np.ndarray) -> Optional[np.ndarray]:
        """Standardize clip to fixed size and frame count"""
        if len(frames) == 0:
            return None
        
        # Temporal standardization: sample to target frame count
        current_frames = len(frames)
        
        if current_frames < self.frame_length:
            # Pad by repeating last frame
            padding_needed = self.frame_length - current_frames
            last_frame = frames[-1:].repeat(padding_needed, axis=0)
            frames = np.concatenate([frames, last_frame], axis=0)
        elif current_frames > self.frame_length:
            # Uniform sampling
            indices = np.linspace(0, current_frames - 1, self.frame_length, dtype=int)
            frames = frames[indices]
        
        # Spatial standardization: resize to target size
        standardized_frames = []
        for frame in frames:
            if frame.shape[:2] != (self.img_size, self.img_size):
                resized = cv2.resize(frame, (self.img_size, self.img_size))
                standardized_frames.append(resized)
            else:
                standardized_frames.append(frame)
        
        standardized_frames = np.array(standardized_frames)
        
        # Pixel normalization: scale to [0, 1]
        standardized_frames = standardized_frames.astype(np.float32) / 255.0
        
        return standardized_frames
    


    def process_single_video(self, s3_key: str, output_name: str) -> Dict[str, any]:
        """Process a single video through the complete pipeline"""
        self.quality_stats['total_processed'] += 1

        # Download video to temporary file
        with tempfile.NamedTemporaryFile(suffix='.mp4', delete=False) as tmp_file:
            temp_path = tmp_file.name

        try:
            # Download from S3
            if not self.s3_manager.download_video(s3_key, temp_path):
                self.quality_stats['processing_error'] += 1
                return {'success': False, 'error': 'download_failed'}

            # Extract frames
            frames = self.extract_frames_from_video(temp_path)
            if frames is None:
                self.quality_stats['processing_error'] += 1
                return {'success': False, 'error': 'frame_extraction_failed'}

            # Process each frame using hybrid cropping strategy
            processed_frames = []
            validation_results = []
            face_bboxes = []

            for frame in frames:
                # Apply hybrid lip crop: YOLO + grid heuristic + MediaPipe validation
                lip_crop, is_valid, has_landmarks, confidence, adjustment_info, face_bbox = self.extract_hybrid_lip_crop(frame)

                validation_results.append({
                    'valid': is_valid,
                    'confidence': confidence,
                    'has_landmarks': has_landmarks,
                    'adjustment': adjustment_info
                })

                processed_frames.append(lip_crop)
                face_bboxes.append(face_bbox)

            if not processed_frames:
                self.quality_stats['processing_error'] += 1
                return {'success': False, 'error': 'no_frames_processed'}

            # Convert to numpy array - all frames should now have the same shape
            try:
                processed_frames = np.array(processed_frames)
            except ValueError as e:
                self.quality_stats['processing_error'] += 1
                return {'success': False, 'error': f'frame_array_creation_failed: {e}'}

            # Auto-scoring system: Calculate landmark coverage percentage
            frames_with_landmarks = sum(1 for r in validation_results if r['has_landmarks'])
            valid_frames = sum(1 for r in validation_results if r['valid'])
            total_frames = len(validation_results)

            # Calculate landmark coverage percentage
            landmark_coverage_pct = (frames_with_landmarks / total_frames * 100) if total_frames > 0 else 0
            valid_coverage_pct = (valid_frames / total_frames * 100) if total_frames > 0 else 0
            avg_confidence = np.mean([r['confidence'] for r in validation_results]) if validation_results else 0

            # Apply 80% threshold for training acceptance
            is_high_quality = landmark_coverage_pct >= 80.0 and valid_coverage_pct >= 80.0

            if not is_high_quality:
                # Mark as low quality but still process (for logging)
                if landmark_coverage_pct < 80.0:
                    self.quality_stats['invalid_crop'] += 1
                    error_reason = 'low_landmark_coverage'
                elif valid_coverage_pct < 80.0:
                    self.quality_stats['low_confidence'] += 1
                    error_reason = 'low_valid_coverage'
                else:
                    self.quality_stats['invalid_crop'] += 1
                    error_reason = 'quality_threshold_not_met'

                # Still process the clip but mark as low quality
                quality_info = {
                    'success': False,
                    'error': error_reason,
                    'landmark_coverage_pct': landmark_coverage_pct,
                    'valid_coverage_pct': valid_coverage_pct,
                    'avg_confidence': avg_confidence,
                    'valid_frames': valid_frames,
                    'total_frames': total_frames,
                    'is_high_quality': False
                }
            else:
                # High quality clip
                quality_info = {
                    'success': True,
                    'landmark_coverage_pct': landmark_coverage_pct,
                    'valid_coverage_pct': valid_coverage_pct,
                    'avg_confidence': avg_confidence,
                    'valid_frames': valid_frames,
                    'total_frames': total_frames,
                    'is_high_quality': True
                }

            # Standardize clip
            standardized_clip = self.standardize_clip(processed_frames)
            if standardized_clip is None:
                self.quality_stats['processing_error'] += 1
                return {'success': False, 'error': 'standardization_failed'}

            # Save processed clip
            output_path = os.path.join(self.processed_data_dir, 'clips', f"{output_name}.npy")
            save_frames_as_npy(standardized_clip, output_path)

            self.quality_stats['successful'] += 1

            # Update quality info with processing details
            quality_info.update({
                'output_path': output_path,
                'original_frames': len(frames),
                'processed_frames': len(standardized_clip),
                'has_landmarks': frames_with_landmarks > 0
            })

            return quality_info

        except Exception as e:
            self.logger.error(f"❌ Error processing {s3_key}: {e}")
            self.quality_stats['processing_error'] += 1
            return {'success': False, 'error': str(e)}

        finally:
            # Clean up temporary file
            if os.path.exists(temp_path):
                os.unlink(temp_path)

    def process_from_manifest(self, manifest_path: str, limit_per_class: Optional[int] = None):
        """Process all videos listed in a manifest file"""
        self.logger.info(f"📋 Processing videos from manifest: {manifest_path}")

        # Load manifest
        df = pd.read_csv(manifest_path)

        # Apply limit per class if specified
        if limit_per_class is not None:
            self.logger.info(f"🔢 Limiting to {limit_per_class} samples per class")
            limited_dfs = []
            for phrase in df['phrase'].unique():
                phrase_df = df[df['phrase'] == phrase].head(limit_per_class)
                limited_dfs.append(phrase_df)
            df = pd.concat(limited_dfs, ignore_index=True)
            self.logger.info(f"📊 Limited dataset size: {len(df)} samples")

        # Process each video
        results = []

        for idx, row in tqdm(df.iterrows(), total=len(df), desc="Processing videos"):
            s3_key = row['s3_key']
            phrase = row['phrase']
            speaker_id = row['speaker_id']

            # Generate output name
            output_name = f"{phrase}_{speaker_id}_{idx:06d}"

            # Process video
            result = self.process_single_video(s3_key, output_name)

            # Add metadata
            result.update({
                'manifest_idx': idx,
                's3_key': s3_key,
                'phrase': phrase,
                'speaker_id': speaker_id,
                'output_name': output_name
            })

            # Record quality data for CSV
            if 'landmark_coverage_pct' in result:
                quality_record = {
                    'clip_id': output_name,
                    'phrase': phrase,
                    'speaker_id': speaker_id,
                    'valid_crop': result.get('is_high_quality', False),
                    'landmark_coverage_pct': result.get('landmark_coverage_pct', 0),
                    'valid_coverage_pct': result.get('valid_coverage_pct', 0),
                    'avg_confidence': result.get('avg_confidence', 0),
                    'valid_frames': result.get('valid_frames', 0),
                    'total_frames': result.get('total_frames', 0),
                    'success': result.get('success', False),
                    'error': result.get('error', '')
                }
                self.quality_records.append(quality_record)

            results.append(result)

        # Save processing log
        log_name = os.path.basename(manifest_path).replace('.csv', '_processing_log.csv')
        log_path = os.path.join(self.processed_data_dir, 'quality_logs', log_name)

        results_df = pd.DataFrame(results)
        results_df.to_csv(log_path, index=False)

        # Save quality CSV with auto-scoring results
        quality_csv_path = os.path.join(self.processed_data_dir, 'quality_logs', 'quality_scores.csv')
        self._save_quality_csv(quality_csv_path)

        # Print statistics
        self._print_processing_stats()

        self.logger.info(f"✅ Processing completed. Log saved to: {log_path}")
        self.logger.info(f"📊 Quality scores saved to: {quality_csv_path}")

    def _save_quality_csv(self, output_path="data_processed/quality_logs/quality_scores.csv"):
        """Save quality records to CSV"""
        if not self.quality_records:
            return

        os.makedirs(os.path.dirname(output_path), exist_ok=True)

        import pandas as pd
        df = pd.DataFrame(self.quality_records)
        df.to_csv(output_path, index=False)

        self.logger.info(f"💾 Quality scores saved to: {output_path}")

        # Print summary statistics
        high_quality_count = len(df[df['valid_crop'] == True])
        total_count = len(df)
        avg_landmark_coverage = df['landmark_coverage_pct'].mean()

        print(f"\n📊 Quality Summary:")
        print("=" * 50)
        print(f"Total clips processed: {total_count}")
        print(f"High quality (≥80%):   {high_quality_count} ({high_quality_count/total_count*100:.1f}%)")
        print(f"Low quality (<80%):    {total_count-high_quality_count} ({(total_count-high_quality_count)/total_count*100:.1f}%)")
        print(f"Avg landmark coverage: {avg_landmark_coverage:.1f}%")
        print("=" * 50)

    def _print_processing_stats(self):
        """Print processing statistics"""
        total = self.quality_stats['total_processed']

        print(f"\n📊 Processing Statistics")
        print("=" * 50)
        print(f"Total processed:     {total:6d}")
        print(f"Successful:          {self.quality_stats['successful']:6d} ({self.quality_stats['successful']/total*100:.1f}%)")
        print(f"Invalid crops:       {self.quality_stats['invalid_crop']:6d} ({self.quality_stats['invalid_crop']/total*100:.1f}%)")
        print(f"No landmarks:        {self.quality_stats['no_landmarks']:6d} ({self.quality_stats['no_landmarks']/total*100:.1f}%)")
        print(f"Low confidence:      {self.quality_stats['low_confidence']:6d} ({self.quality_stats['low_confidence']/total*100:.1f}%)")
        print(f"Processing errors:   {self.quality_stats['processing_error']:6d} ({self.quality_stats['processing_error']/total*100:.1f}%)")
        print("=" * 50)


def main():
    parser = argparse.ArgumentParser(description='Preprocess videos using YOLO→SAM pipeline')
    parser.add_argument('--config', default='configs/training.yaml', help='Config file path')
    parser.add_argument('--manifest', required=True, help='Manifest CSV file to process')
    parser.add_argument('--limit_per_class', type=int, help='Limit number of samples per class (for testing)')
    args = parser.parse_args()

    # Load configuration
    config = load_config(args.config)

    # Set seed for reproducibility
    set_seed(config.get('seed', 42))

    # Process videos
    preprocessor = VideoPreprocessor(config)
    preprocessor.process_from_manifest(args.manifest, limit_per_class=args.limit_per_class)


if __name__ == "__main__":
    main()
