"""
PyTorch Dataset for ICU Lipreading
Loads processed clips from manifests with proper transforms
"""

import os
import pandas as pd
import numpy as np
import torch
from torch.utils.data import Dataset, DataLoader
from typing import Dict, List, Tuple, Optional, Callable
import cv2
from sklearn.preprocessing import LabelEncoder

from utils import load_frames_from_npy, setup_logging


class LipreadingDataset(Dataset):
    """PyTorch Dataset for lipreading clips"""
    
    def __init__(
        self,
        manifest_path: str,
        processed_data_dir: str,
        target_phrases: List[str],
        transform: Optional[Callable] = None,
        augment: bool = False
    ):
        """
        Args:
            manifest_path: Path to CSV manifest file
            processed_data_dir: Directory containing processed .npy clips
            target_phrases: List of target phrase classes
            transform: Optional transform function
            augment: Whether to apply data augmentation
        """
        self.processed_data_dir = processed_data_dir
        self.transform = transform
        self.augment = augment
        self.logger = setup_logging()
        
        # Load manifest
        self.manifest = pd.read_csv(manifest_path)
        self.logger.info(f"📋 Loaded manifest with {len(self.manifest)} entries")
        
        # Filter successful processing only
        if 'success' in self.manifest.columns:
            successful_mask = self.manifest['success'] == True
            self.manifest = self.manifest[successful_mask].reset_index(drop=True)
            self.logger.info(f"✅ Filtered to {len(self.manifest)} successfully processed clips")
        
        # Setup label encoding
        self.target_phrases = target_phrases
        self.label_encoder = LabelEncoder()
        self.label_encoder.fit(target_phrases)
        
        # Encode labels
        self.labels = self.label_encoder.transform(self.manifest['phrase'].values)
        
        # Print class distribution
        self._print_class_distribution()
    
    def _print_class_distribution(self):
        """Print class distribution in the dataset"""
        from collections import Counter
        
        phrase_counts = Counter(self.manifest['phrase'].values)
        total = len(self.manifest)
        
        print(f"\n📊 Dataset Class Distribution")
        print("=" * 40)
        for phrase in self.target_phrases:
            count = phrase_counts.get(phrase, 0)
            percentage = (count / total) * 100 if total > 0 else 0
            print(f"{phrase:20} {count:6d} ({percentage:5.1f}%)")
        print(f"{'Total':20} {total:6d} (100.0%)")
        print("=" * 40)
    
    def __len__(self) -> int:
        return len(self.manifest)
    
    def __getitem__(self, idx: int) -> Tuple[torch.Tensor, int, Dict]:
        """
        Returns:
            clip: Tensor of shape (T, H, W, C) - frames, height, width, channels
            label: Integer class label
            metadata: Dictionary with additional information
        """
        row = self.manifest.iloc[idx]
        
        # Load processed clip
        clip_name = row.get('output_name', f"clip_{idx:06d}")
        clip_path = os.path.join(self.processed_data_dir, 'clips', f"{clip_name}.npy")
        
        try:
            # Load frames (T, H, W, C) in float32 [0, 1] range
            clip = load_frames_from_npy(clip_path)
            
            # Apply augmentation if enabled
            if self.augment:
                clip = self._apply_augmentation(clip)
            
            # Apply custom transform if provided
            if self.transform:
                clip = self.transform(clip)
            
            # Convert to tensor and rearrange to (C, T, H, W) for 3D CNN
            clip_tensor = torch.from_numpy(clip).float()
            clip_tensor = clip_tensor.permute(3, 0, 1, 2)  # (T,H,W,C) -> (C,T,H,W)
            
            # Get label
            label = self.labels[idx]
            
            # Metadata
            metadata = {
                'phrase': row['phrase'],
                'speaker_id': row['speaker_id'],
                's3_key': row['s3_key'],
                'clip_path': clip_path,
                'idx': idx
            }
            
            return clip_tensor, label, metadata
            
        except Exception as e:
            self.logger.error(f"❌ Error loading clip {clip_path}: {e}")
            
            # Return dummy data to avoid breaking the batch
            dummy_clip = torch.zeros((3, 16, 96, 96))  # (C, T, H, W)
            return dummy_clip, 0, {'error': str(e), 'idx': idx}
    
    def _apply_augmentation(self, clip: np.ndarray) -> np.ndarray:
        """Apply data augmentation to clip"""
        # Random horizontal flip
        if np.random.random() > 0.5:
            clip = np.flip(clip, axis=2)  # Flip width dimension
        
        # Random brightness adjustment
        if np.random.random() > 0.5:
            brightness_factor = np.random.uniform(0.8, 1.2)
            clip = np.clip(clip * brightness_factor, 0, 1)
        
        # Random contrast adjustment
        if np.random.random() > 0.5:
            contrast_factor = np.random.uniform(0.8, 1.2)
            mean = np.mean(clip)
            clip = np.clip((clip - mean) * contrast_factor + mean, 0, 1)
        
        # Random temporal shift (circular)
        if np.random.random() > 0.5:
            shift = np.random.randint(-2, 3)  # Shift by up to 2 frames
            clip = np.roll(clip, shift, axis=0)
        
        return clip
    
    def get_class_weights(self) -> torch.Tensor:
        """Calculate class weights for imbalanced data"""
        from collections import Counter
        
        label_counts = Counter(self.labels)
        total_samples = len(self.labels)
        num_classes = len(self.target_phrases)
        
        weights = []
        for i in range(num_classes):
            count = label_counts.get(i, 1)  # Avoid division by zero
            weight = total_samples / (num_classes * count)
            weights.append(weight)
        
        return torch.FloatTensor(weights)
    
    def get_phrase_from_label(self, label: int) -> str:
        """Convert integer label back to phrase"""
        return self.label_encoder.inverse_transform([label])[0]


def create_data_loaders(
    config: Dict,
    batch_size: Optional[int] = None,
    num_workers: int = 4
) -> Tuple[DataLoader, DataLoader, DataLoader]:
    """Create train, validation, and test data loaders"""
    
    if batch_size is None:
        batch_size = config['training']['batch_size']
    
    manifests_dir = config['data']['manifests_dir']
    processed_data_dir = config['data']['processed_data_dir']
    target_phrases = config['data']['target_phrases']
    
    # Create datasets
    train_dataset = LipreadingDataset(
        manifest_path=os.path.join(manifests_dir, 'train.csv'),
        processed_data_dir=processed_data_dir,
        target_phrases=target_phrases,
        augment=True  # Enable augmentation for training
    )
    
    val_dataset = LipreadingDataset(
        manifest_path=os.path.join(manifests_dir, 'val.csv'),
        processed_data_dir=processed_data_dir,
        target_phrases=target_phrases,
        augment=False  # No augmentation for validation
    )
    
    test_dataset = LipreadingDataset(
        manifest_path=os.path.join(manifests_dir, 'test.csv'),
        processed_data_dir=processed_data_dir,
        target_phrases=target_phrases,
        augment=False  # No augmentation for testing
    )
    
    # Create data loaders
    train_loader = DataLoader(
        train_dataset,
        batch_size=batch_size,
        shuffle=True,
        num_workers=num_workers,
        pin_memory=torch.cuda.is_available(),
        drop_last=True  # Ensure consistent batch sizes
    )
    
    val_loader = DataLoader(
        val_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=torch.cuda.is_available(),
        drop_last=False
    )
    
    test_loader = DataLoader(
        test_dataset,
        batch_size=batch_size,
        shuffle=False,
        num_workers=num_workers,
        pin_memory=torch.cuda.is_available(),
        drop_last=False
    )
    
    return train_loader, val_loader, test_loader


def collate_fn(batch):
    """Custom collate function to handle potential errors in data loading"""
    # Filter out error samples
    valid_samples = [(clip, label, meta) for clip, label, meta in batch if 'error' not in meta]
    
    if not valid_samples:
        # Return dummy batch if all samples failed
        dummy_clips = torch.zeros((1, 3, 16, 96, 96))
        dummy_labels = torch.zeros(1, dtype=torch.long)
        dummy_metadata = [{'error': 'all_samples_failed'}]
        return dummy_clips, dummy_labels, dummy_metadata
    
    # Separate components
    clips, labels, metadata = zip(*valid_samples)
    
    # Stack tensors
    clips_tensor = torch.stack(clips)
    labels_tensor = torch.tensor(labels, dtype=torch.long)
    
    return clips_tensor, labels_tensor, list(metadata)


# Example usage and testing
if __name__ == "__main__":
    from utils import load_config
    
    # Load config
    config = load_config('configs/training.yaml')
    
    # Test dataset creation
    try:
        train_loader, val_loader, test_loader = create_data_loaders(config, batch_size=4)
        
        print("✅ Data loaders created successfully!")
        print(f"Train batches: {len(train_loader)}")
        print(f"Val batches: {len(val_loader)}")
        print(f"Test batches: {len(test_loader)}")
        
        # Test loading a batch
        for clips, labels, metadata in train_loader:
            print(f"Batch shape: {clips.shape}")  # Should be (B, C, T, H, W)
            print(f"Labels shape: {labels.shape}")
            print(f"Sample phrases: {[meta.get('phrase', 'unknown') for meta in metadata[:3]]}")
            break
            
    except Exception as e:
        print(f"❌ Error testing data loaders: {e}")
        import traceback
        traceback.print_exc()
