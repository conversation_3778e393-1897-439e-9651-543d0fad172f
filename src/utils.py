"""
Utility functions for ICU Lipreading Pipeline
Includes S3 operations, logging, seed control, and common helpers
"""

import os
import logging
import random
import hashlib
import yaml
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
import numpy as np
import torch
import boto3
from botocore.exceptions import ClientError, NoCredentialsError
import cv2
from tqdm import tqdm


def setup_logging(log_level: str = "INFO", log_dir: str = "./logs") -> logging.Logger:
    """Setup logging configuration"""
    os.makedirs(log_dir, exist_ok=True)
    
    # Create logger
    logger = logging.getLogger("lipreading")
    logger.setLevel(getattr(logging, log_level.upper()))
    
    # Avoid duplicate handlers
    if logger.handlers:
        return logger
    
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(getattr(logging, log_level.upper()))
    
    # File handler
    file_handler = logging.FileHandler(os.path.join(log_dir, "pipeline.log"))
    file_handler.setLevel(logging.DEBUG)
    
    # Formatter
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    console_handler.setFormatter(formatter)
    file_handler.setFormatter(formatter)
    
    logger.addHandler(console_handler)
    logger.addHandler(file_handler)
    
    return logger


def set_seed(seed: int = 42):
    """Set random seeds for reproducibility"""
    random.seed(seed)
    np.random.seed(seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False
    os.environ['PYTHONHASHSEED'] = str(seed)


def load_config(config_path: str = "configs/training.yaml") -> Dict[str, Any]:
    """Load YAML configuration file"""
    with open(config_path, 'r') as f:
        config = yaml.safe_load(f)
    return config


class S3Manager:
    """Handle S3 operations for the lipreading pipeline"""
    
    def __init__(self, bucket_name: str):
        self.bucket_name = bucket_name
        self.logger = logging.getLogger("lipreading.s3")
        
        try:
            self.s3_client = boto3.client('s3')
            self.s3_resource = boto3.resource('s3')
            self.bucket = self.s3_resource.Bucket(bucket_name)
            
            # Test connection
            self.s3_client.head_bucket(Bucket=bucket_name)
            self.logger.info(f"✅ Connected to S3 bucket: {bucket_name}")
            
        except NoCredentialsError:
            self.logger.error("❌ AWS credentials not found. Run 'aws configure'")
            raise
        except ClientError as e:
            self.logger.error(f"❌ S3 connection failed: {e}")
            raise
    
    def list_videos(self, prefix: str = "") -> List[str]:
        """List all video files in the S3 bucket"""
        video_extensions = {'.mp4', '.avi', '.mov', '.mkv', '.webm'}
        video_keys = []
        
        try:
            paginator = self.s3_client.get_paginator('list_objects_v2')
            pages = paginator.paginate(Bucket=self.bucket_name, Prefix=prefix)
            
            for page in pages:
                if 'Contents' in page:
                    for obj in page['Contents']:
                        key = obj['Key']
                        if any(key.lower().endswith(ext) for ext in video_extensions):
                            video_keys.append(key)
            
            self.logger.info(f"📹 Found {len(video_keys)} videos in s3://{self.bucket_name}/{prefix}")
            return video_keys
            
        except ClientError as e:
            self.logger.error(f"❌ Failed to list S3 objects: {e}")
            return []
    
    def download_video(self, s3_key: str, local_path: str) -> bool:
        """Download a single video from S3"""
        try:
            os.makedirs(os.path.dirname(local_path), exist_ok=True)
            self.s3_client.download_file(self.bucket_name, s3_key, local_path)
            return True
        except ClientError as e:
            self.logger.error(f"❌ Failed to download {s3_key}: {e}")
            return False
    
    def get_video_metadata(self, s3_key: str) -> Dict[str, Any]:
        """Get metadata for a video file"""
        try:
            response = self.s3_client.head_object(Bucket=self.bucket_name, Key=s3_key)
            return {
                'size': response['ContentLength'],
                'last_modified': response['LastModified'],
                'etag': response['ETag'].strip('"')
            }
        except ClientError:
            return {}


def parse_phrase_from_filename(filename: str) -> Optional[str]:
    """Extract phrase from filename (before double underscore)"""
    basename = os.path.basename(filename)
    if '__' in basename:
        phrase = basename.split('__')[0]
        # Handle folder structure - get the last part if it contains '/'
        if '/' in phrase:
            phrase = phrase.split('/')[-1]
        return phrase.lower()
    return None


def generate_speaker_id(filename: str) -> str:
    """Generate consistent speaker ID from filename/path"""
    # Use the full path to generate speaker ID, but exclude the phrase part
    path_parts = filename.split('/')
    
    # Remove the phrase part (before __) from the last component
    if '__' in path_parts[-1]:
        last_part = path_parts[-1].split('__', 1)[1]  # Everything after first __
        path_parts[-1] = last_part
    
    # Create speaker ID from remaining path components
    speaker_path = '/'.join(path_parts[:-1]) + '/' + path_parts[-1]
    
    # Hash to create consistent but anonymous speaker ID
    return hashlib.md5(speaker_path.encode()).hexdigest()[:8]


def create_directories(paths: List[str]):
    """Create multiple directories if they don't exist"""
    for path in paths:
        os.makedirs(path, exist_ok=True)


def get_video_info(video_path: str) -> Dict[str, Any]:
    """Get basic video information using OpenCV"""
    cap = cv2.VideoCapture(video_path)
    
    if not cap.isOpened():
        return {'valid': False}
    
    info = {
        'valid': True,
        'frame_count': int(cap.get(cv2.CAP_PROP_FRAME_COUNT)),
        'fps': cap.get(cv2.CAP_PROP_FPS),
        'width': int(cap.get(cv2.CAP_PROP_FRAME_WIDTH)),
        'height': int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT)),
        'duration': 0
    }
    
    if info['fps'] > 0:
        info['duration'] = info['frame_count'] / info['fps']
    
    cap.release()
    return info


def calculate_motion_score(frames: np.ndarray) -> float:
    """Calculate motion score between consecutive frames"""
    if len(frames) < 2:
        return 0.0
    
    motion_scores = []
    for i in range(1, len(frames)):
        # Convert to grayscale if needed
        if len(frames[i].shape) == 3:
            frame1 = cv2.cvtColor(frames[i-1], cv2.COLOR_RGB2GRAY)
            frame2 = cv2.cvtColor(frames[i], cv2.COLOR_RGB2GRAY)
        else:
            frame1, frame2 = frames[i-1], frames[i]
        
        # Calculate optical flow magnitude
        flow = cv2.calcOpticalFlowPyrLK(
            frame1, frame2, 
            np.array([[frame1.shape[1]//2, frame1.shape[0]//2]], dtype=np.float32),
            None
        )[0]
        
        if flow is not None:
            motion_scores.append(np.linalg.norm(flow))
    
    return np.mean(motion_scores) if motion_scores else 0.0


def calculate_blur_score(image: np.ndarray) -> float:
    """Calculate blur score using Laplacian variance"""
    if len(image.shape) == 3:
        gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
    else:
        gray = image
    
    return cv2.Laplacian(gray, cv2.CV_64F).var()


def save_frames_as_npy(frames: np.ndarray, output_path: str):
    """Save frames as numpy array"""
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    np.save(output_path, frames)


def load_frames_from_npy(npy_path: str) -> np.ndarray:
    """Load frames from numpy array"""
    return np.load(npy_path)


def print_class_distribution(labels: List[str], title: str = "Class Distribution"):
    """Print formatted class distribution"""
    from collections import Counter
    
    counter = Counter(labels)
    total = len(labels)
    
    print(f"\n{title}")
    print("=" * 50)
    for phrase, count in counter.most_common():
        percentage = (count / total) * 100
        print(f"{phrase:20} {count:6d} ({percentage:5.1f}%)")
    print(f"{'Total':20} {total:6d} (100.0%)")
    print("=" * 50)


def get_device() -> torch.device:
    """Get the best available device"""
    if torch.cuda.is_available():
        device = torch.device('cuda')
        print(f"🚀 Using GPU: {torch.cuda.get_device_name()}")
    else:
        device = torch.device('cpu')
        print("💻 Using CPU")
    
    return device
