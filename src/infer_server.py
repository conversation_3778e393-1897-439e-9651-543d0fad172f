"""
FastAPI Inference Server for ICU Lipreading
Processes video uploads and returns phrase predictions
"""

import os
import time
import tempfile
import numpy as np
import torch
import cv2
from fastapi import FastAPI, File, UploadFile, HTTPException
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from typing import List, Dict, Optional
import uvicorn
from ultralytics import YOLO
from segment_anything import SamPredictor, sam_model_registry

from utils import setup_logging, load_config, get_device
from model import create_model


class PredictionResponse(BaseModel):
    phrase: str
    confidence: float
    top5: List[Dict[str, float]]
    latency_ms: float
    processing_info: Dict[str, any]


class InferenceEngine:
    """Handles model loading and inference"""
    
    def __init__(self, config: dict, checkpoint_path: str):
        self.config = config
        self.logger = setup_logging()
        
        # Configuration
        self.target_phrases = config['data']['target_phrases']
        self.img_size = config['data']['img_size']
        self.frame_length = config['data']['frame_length']
        self.device = get_device()
        
        # Quality thresholds
        self.min_face_area = config['segmentation']['quality']['min_face_area']
        
        # Load models
        self._load_models(checkpoint_path)
        
        self.logger.info("🚀 Inference engine ready!")
    
    def _load_models(self, checkpoint_path: str):
        """Load all required models"""
        self.logger.info("🤖 Loading models...")
        
        # Load main model
        self.model = create_model(self.config)
        
        if os.path.exists(checkpoint_path):
            checkpoint = torch.load(checkpoint_path, map_location=self.device)
            self.model.load_state_dict(checkpoint['model_state_dict'])
            self.logger.info(f"✅ Loaded model from {checkpoint_path}")
        else:
            self.logger.warning(f"⚠️ Checkpoint not found: {checkpoint_path}, using random weights")
        
        self.model.to(self.device)
        self.model.eval()
        
        # Load YOLO
        yolo_model = self.config['segmentation']['yolo']['model']
        self.yolo = YOLO(yolo_model)
        self.yolo_conf = self.config['segmentation']['yolo']['conf_threshold']
        
        # Load SAM
        sam_type = self.config['segmentation']['sam']['model_type']
        sam_checkpoint = self.config['segmentation']['sam']['checkpoint_path']
        
        if os.path.exists(sam_checkpoint):
            sam = sam_model_registry[sam_type](checkpoint=sam_checkpoint)
            sam.to(self.device)
            self.sam_predictor = SamPredictor(sam)
            self.logger.info("✅ SAM model loaded")
        else:
            self.sam_predictor = None
            self.logger.warning("⚠️ SAM checkpoint not found, using YOLO only")
    
    def extract_frames_from_video(self, video_path: str) -> Optional[np.ndarray]:
        """Extract frames from video file"""
        cap = cv2.VideoCapture(video_path)
        
        if not cap.isOpened():
            return None
        
        frames = []
        while True:
            ret, frame = cap.read()
            if not ret:
                break
            
            # Convert BGR to RGB
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            frames.append(frame_rgb)
        
        cap.release()
        
        if not frames:
            return None
        
        return np.array(frames)
    
    def detect_face_with_yolo(self, frame: np.ndarray) -> Optional[tuple]:
        """Detect face using YOLO"""
        results = self.yolo(frame, conf=self.yolo_conf, classes=[0], verbose=False)
        
        if not results or not results[0].boxes:
            return None
        
        # Get the largest detection
        boxes = results[0].boxes.xyxy.cpu().numpy()
        areas = [(box[2] - box[0]) * (box[3] - box[1]) for box in boxes]
        
        if not areas:
            return None
        
        largest_idx = np.argmax(areas)
        box = boxes[largest_idx]
        
        # Focus on upper half for face region
        x1, y1, x2, y2 = map(int, box)
        face_height = y2 - y1
        face_y2 = y1 + int(face_height * 0.6)
        
        return (x1, y1, x2, face_y2)
    
    def segment_lips_with_sam(self, frame: np.ndarray, face_bbox: tuple) -> Optional[np.ndarray]:
        """Segment lips using SAM"""
        if self.sam_predictor is None:
            return None
        
        x1, y1, x2, y2 = face_bbox
        face_crop = frame[y1:y2, x1:x2]
        
        if face_crop.size == 0:
            return None
        
        try:
            self.sam_predictor.set_image(face_crop)
            
            # Create prompt point in lower center of face
            face_h, face_w = face_crop.shape[:2]
            lip_point = np.array([[face_w // 2, int(face_h * 0.75)]])
            lip_label = np.array([1])
            
            masks, scores, _ = self.sam_predictor.predict(
                point_coords=lip_point,
                point_labels=lip_label,
                multimask_output=True
            )
            
            if len(masks) == 0:
                return None
            
            # Select best mask
            best_mask = masks[np.argmax(scores)]
            
            # Apply mask
            masked_face = face_crop.copy()
            masked_face[~best_mask] = 0
            
            return masked_face
            
        except Exception:
            return None
    
    def preprocess_video(self, video_path: str) -> Optional[np.ndarray]:
        """Preprocess video through YOLO→SAM pipeline"""
        # Extract frames
        frames = self.extract_frames_from_video(video_path)
        if frames is None:
            return None
        
        # Process each frame
        processed_frames = []
        face_detected = False
        
        for frame in frames:
            # YOLO face detection
            face_bbox = self.detect_face_with_yolo(frame)
            
            if face_bbox is None:
                processed_frames.append(frame)
                continue
            
            face_detected = True
            
            # Check face size
            x1, y1, x2, y2 = face_bbox
            face_area = (x2 - x1) * (y2 - y1)
            
            if face_area < self.min_face_area:
                processed_frames.append(frame)
                continue
            
            # SAM lip segmentation
            lip_region = self.segment_lips_with_sam(frame, face_bbox)
            
            if lip_region is not None:
                processed_frames.append(lip_region)
            else:
                # Fallback to face crop
                face_crop = frame[y1:y2, x1:x2]
                processed_frames.append(face_crop)
        
        if not processed_frames or not face_detected:
            return None
        
        processed_frames = np.array(processed_frames)
        
        # Standardize clip
        return self._standardize_clip(processed_frames)
    
    def _standardize_clip(self, frames: np.ndarray) -> Optional[np.ndarray]:
        """Standardize clip to fixed size and frame count"""
        if len(frames) == 0:
            return None
        
        # Temporal standardization
        current_frames = len(frames)
        
        if current_frames < self.frame_length:
            # Pad by repeating last frame
            padding_needed = self.frame_length - current_frames
            last_frame = frames[-1:].repeat(padding_needed, axis=0)
            frames = np.concatenate([frames, last_frame], axis=0)
        elif current_frames > self.frame_length:
            # Uniform sampling
            indices = np.linspace(0, current_frames - 1, self.frame_length, dtype=int)
            frames = frames[indices]
        
        # Spatial standardization
        standardized_frames = []
        for frame in frames:
            if frame.shape[:2] != (self.img_size, self.img_size):
                resized = cv2.resize(frame, (self.img_size, self.img_size))
                standardized_frames.append(resized)
            else:
                standardized_frames.append(frame)
        
        standardized_frames = np.array(standardized_frames)
        
        # Pixel normalization
        standardized_frames = standardized_frames.astype(np.float32) / 255.0
        
        return standardized_frames
    
    def predict(self, video_path: str) -> Dict:
        """Run inference on video"""
        start_time = time.time()
        
        # Preprocess video
        processed_clip = self.preprocess_video(video_path)
        
        if processed_clip is None:
            return {
                'success': False,
                'error': 'preprocessing_failed',
                'message': 'Could not detect face or process video'
            }
        
        # Convert to tensor and add batch dimension
        clip_tensor = torch.from_numpy(processed_clip).float()
        clip_tensor = clip_tensor.permute(3, 0, 1, 2)  # (T,H,W,C) -> (C,T,H,W)
        clip_tensor = clip_tensor.unsqueeze(0).to(self.device)  # Add batch dim
        
        # Run inference
        with torch.no_grad():
            outputs = self.model(clip_tensor)
            probabilities = torch.softmax(outputs, dim=1)
            
            # Get top predictions
            top_probs, top_indices = torch.topk(probabilities, k=min(5, len(self.target_phrases)))
            
            top_predictions = []
            for prob, idx in zip(top_probs[0], top_indices[0]):
                phrase = self.target_phrases[idx.item()]
                confidence = prob.item()
                top_predictions.append({
                    'phrase': phrase,
                    'confidence': confidence
                })
        
        # Calculate latency
        latency_ms = (time.time() - start_time) * 1000
        
        return {
            'success': True,
            'phrase': top_predictions[0]['phrase'],
            'confidence': top_predictions[0]['confidence'],
            'top5': top_predictions,
            'latency_ms': latency_ms,
            'processing_info': {
                'original_frames': len(processed_clip),
                'processed_frames': self.frame_length,
                'input_shape': list(clip_tensor.shape)
            }
        }


# Initialize FastAPI app
app = FastAPI(
    title="ICU Lipreading API",
    description="AI-powered lipreading for ICU patients",
    version="1.0.0"
)

# Global inference engine
inference_engine = None


@app.on_event("startup")
async def startup_event():
    """Initialize inference engine on startup"""
    global inference_engine
    
    # Load configuration
    config = load_config('configs/training.yaml')
    checkpoint_path = 'checkpoints/best_model.pth'
    
    # Initialize inference engine
    inference_engine = InferenceEngine(config, checkpoint_path)


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy",
        "model_loaded": inference_engine is not None,
        "timestamp": time.time()
    }


@app.post("/predict", response_model=PredictionResponse)
async def predict_video(video: UploadFile = File(...)):
    """Predict phrase from uploaded video"""
    
    if inference_engine is None:
        raise HTTPException(status_code=503, detail="Model not loaded")
    
    # Validate file type
    if not video.content_type.startswith('video/'):
        raise HTTPException(status_code=400, detail="File must be a video")
    
    # Save uploaded file temporarily
    with tempfile.NamedTemporaryFile(delete=False, suffix='.mp4') as tmp_file:
        content = await video.read()
        tmp_file.write(content)
        tmp_file_path = tmp_file.name
    
    try:
        # Run inference
        result = inference_engine.predict(tmp_file_path)
        
        if not result['success']:
            raise HTTPException(
                status_code=422, 
                detail=f"Processing failed: {result.get('message', 'Unknown error')}"
            )
        
        return PredictionResponse(
            phrase=result['phrase'],
            confidence=result['confidence'],
            top5=result['top5'],
            latency_ms=result['latency_ms'],
            processing_info=result['processing_info']
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Inference failed: {str(e)}")
    
    finally:
        # Clean up temporary file
        if os.path.exists(tmp_file_path):
            os.unlink(tmp_file_path)


@app.get("/classes")
async def get_classes():
    """Get list of supported phrases"""
    if inference_engine is None:
        raise HTTPException(status_code=503, detail="Model not loaded")
    
    return {
        "classes": inference_engine.target_phrases,
        "num_classes": len(inference_engine.target_phrases)
    }


def main():
    """Run the FastAPI server"""
    config = load_config('configs/training.yaml')
    api_config = config['inference']['api']
    
    uvicorn.run(
        "infer_server:app",
        host=api_config['host'],
        port=api_config['port'],
        reload=False,
        log_level="info"
    )


if __name__ == "__main__":
    main()
