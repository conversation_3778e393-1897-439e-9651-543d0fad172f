"""
Manifest Builder for ICU Lipreading Pipeline
Creates speaker-disjoint train/val/test splits from S3 video data
"""

import os
import pandas as pd
import numpy as np
from collections import defaultdict, Counter
from typing import Dict, List, Tuple, Set
import argparse
from sklearn.model_selection import train_test_split

from utils import (
    setup_logging, set_seed, load_config, S3Manager,
    parse_phrase_from_filename, generate_speaker_id, 
    create_directories, print_class_distribution
)


class ManifestBuilder:
    """Build speaker-disjoint manifests for training/validation/test splits"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.logger = setup_logging(
            config.get('logging', {}).get('level', 'INFO'),
            config.get('logging', {}).get('log_dir', './logs')
        )
        
        # Initialize S3 manager
        self.s3_manager = S3Manager(config['data']['s3_bucket'])
        
        # Target phrases
        self.target_phrases = set(config['data']['target_phrases'])
        
        # Split ratios
        self.train_ratio = config['data']['train_ratio']
        self.val_ratio = config['data']['val_ratio'] 
        self.test_ratio = config['data']['test_ratio']
        
        # Output directory
        self.manifests_dir = config['data']['manifests_dir']
        create_directories([self.manifests_dir])
    
    def enumerate_s3_videos(self) -> List[Dict]:
        """Enumerate all videos from S3 and extract metadata"""
        self.logger.info("🔍 Enumerating S3 videos...")
        
        video_keys = self.s3_manager.list_videos(self.config['data']['s3_prefix'])
        
        video_data = []
        valid_count = 0
        
        for key in video_keys:
            # Parse phrase from filename
            phrase = parse_phrase_from_filename(key)
            
            # Skip if not in target phrases
            if phrase not in self.target_phrases:
                continue
            
            # Generate speaker ID
            speaker_id = generate_speaker_id(key)
            
            # Get video metadata
            metadata = self.s3_manager.get_video_metadata(key)
            
            video_info = {
                's3_key': key,
                'phrase': phrase,
                'speaker_id': speaker_id,
                'file_size': metadata.get('size', 0),
                'last_modified': metadata.get('last_modified', ''),
                'etag': metadata.get('etag', '')
            }
            
            video_data.append(video_info)
            valid_count += 1
        
        self.logger.info(f"✅ Found {valid_count} valid videos for target phrases")
        return video_data
    
    def analyze_speaker_distribution(self, video_data: List[Dict]) -> Dict:
        """Analyze speaker and phrase distribution"""
        self.logger.info("📊 Analyzing speaker and phrase distribution...")
        
        # Count by phrase
        phrase_counts = Counter([v['phrase'] for v in video_data])
        
        # Count by speaker
        speaker_counts = Counter([v['speaker_id'] for v in video_data])
        
        # Count videos per speaker per phrase
        speaker_phrase_counts = defaultdict(lambda: defaultdict(int))
        for video in video_data:
            speaker_phrase_counts[video['speaker_id']][video['phrase']] += 1
        
        analysis = {
            'total_videos': len(video_data),
            'total_speakers': len(speaker_counts),
            'phrase_counts': dict(phrase_counts),
            'speaker_counts': dict(speaker_counts),
            'speaker_phrase_matrix': dict(speaker_phrase_counts)
        }
        
        # Print distributions
        print_class_distribution(
            [v['phrase'] for v in video_data], 
            "Phrase Distribution"
        )
        
        print(f"\n📈 Speaker Statistics")
        print("=" * 50)
        print(f"Total speakers: {analysis['total_speakers']}")
        print(f"Videos per speaker: {len(video_data) / analysis['total_speakers']:.1f} avg")
        print(f"Min videos per speaker: {min(speaker_counts.values())}")
        print(f"Max videos per speaker: {max(speaker_counts.values())}")
        
        return analysis
    
    def create_speaker_disjoint_splits(self, video_data: List[Dict]) -> Tuple[List[Dict], List[Dict], List[Dict]]:
        """Create speaker-disjoint train/val/test splits"""
        self.logger.info("🔀 Creating speaker-disjoint splits...")
        
        # Group videos by speaker
        speaker_videos = defaultdict(list)
        for video in video_data:
            speaker_videos[video['speaker_id']].append(video)
        
        speakers = list(speaker_videos.keys())
        
        # Calculate videos per speaker for stratification
        speaker_video_counts = {s: len(videos) for s, videos in speaker_videos.items()}
        
        # First split: train vs (val + test)
        train_speakers, temp_speakers = train_test_split(
            speakers,
            test_size=(self.val_ratio + self.test_ratio),
            random_state=self.config.get('seed', 42),
            stratify=None  # Can't stratify on continuous values easily
        )
        
        # Second split: val vs test
        val_speakers, test_speakers = train_test_split(
            temp_speakers,
            test_size=self.test_ratio / (self.val_ratio + self.test_ratio),
            random_state=self.config.get('seed', 42)
        )
        
        # Collect videos for each split
        train_videos = []
        val_videos = []
        test_videos = []
        
        for speaker in train_speakers:
            train_videos.extend(speaker_videos[speaker])
        
        for speaker in val_speakers:
            val_videos.extend(speaker_videos[speaker])
            
        for speaker in test_speakers:
            test_videos.extend(speaker_videos[speaker])
        
        # Verify no speaker overlap
        train_speaker_set = set(train_speakers)
        val_speaker_set = set(val_speakers)
        test_speaker_set = set(test_speakers)
        
        assert len(train_speaker_set & val_speaker_set) == 0, "Speaker overlap between train and val!"
        assert len(train_speaker_set & test_speaker_set) == 0, "Speaker overlap between train and test!"
        assert len(val_speaker_set & test_speaker_set) == 0, "Speaker overlap between val and test!"
        
        # Print split statistics
        print(f"\n🎯 Split Statistics")
        print("=" * 50)
        print(f"Train: {len(train_videos):4d} videos, {len(train_speakers):3d} speakers ({len(train_videos)/len(video_data)*100:.1f}%)")
        print(f"Val:   {len(val_videos):4d} videos, {len(val_speakers):3d} speakers ({len(val_videos)/len(video_data)*100:.1f}%)")
        print(f"Test:  {len(test_videos):4d} videos, {len(test_speakers):3d} speakers ({len(test_videos)/len(video_data)*100:.1f}%)")
        
        return train_videos, val_videos, test_videos
    
    def calculate_class_weights(self, train_videos: List[Dict]) -> Dict[str, float]:
        """Calculate class weights for imbalanced data"""
        phrase_counts = Counter([v['phrase'] for v in train_videos])
        total_samples = len(train_videos)
        num_classes = len(phrase_counts)
        
        # Calculate inverse frequency weights
        class_weights = {}
        for phrase in self.target_phrases:
            count = phrase_counts.get(phrase, 1)  # Avoid division by zero
            weight = total_samples / (num_classes * count)
            class_weights[phrase] = weight
        
        return class_weights
    
    def save_manifests(self, train_videos: List[Dict], val_videos: List[Dict], test_videos: List[Dict]):
        """Save train/val/test manifests as CSV files"""
        self.logger.info("💾 Saving manifests...")
        
        def videos_to_dataframe(videos: List[Dict]) -> pd.DataFrame:
            return pd.DataFrame(videos)
        
        # Convert to DataFrames
        train_df = videos_to_dataframe(train_videos)
        val_df = videos_to_dataframe(val_videos)
        test_df = videos_to_dataframe(test_videos)
        
        # Save CSV files
        train_df.to_csv(os.path.join(self.manifests_dir, 'train.csv'), index=False)
        val_df.to_csv(os.path.join(self.manifests_dir, 'val.csv'), index=False)
        test_df.to_csv(os.path.join(self.manifests_dir, 'test.csv'), index=False)
        
        # Calculate and save class weights
        class_weights = self.calculate_class_weights(train_videos)
        weights_df = pd.DataFrame([
            {'phrase': phrase, 'weight': weight} 
            for phrase, weight in class_weights.items()
        ])
        weights_df.to_csv(os.path.join(self.manifests_dir, 'class_weights.csv'), index=False)
        
        self.logger.info(f"✅ Saved manifests to {self.manifests_dir}/")
        
        # Print per-split class distributions
        for split_name, videos in [('Train', train_videos), ('Val', val_videos), ('Test', test_videos)]:
            print_class_distribution(
                [v['phrase'] for v in videos],
                f"{split_name} Split Distribution"
            )
    
    def build_manifests(self):
        """Main method to build all manifests"""
        self.logger.info("🚀 Starting manifest building process...")
        
        # Enumerate S3 videos
        video_data = self.enumerate_s3_videos()
        
        if not video_data:
            self.logger.error("❌ No valid videos found!")
            return
        
        # Analyze distribution
        analysis = self.analyze_speaker_distribution(video_data)
        
        # Create splits
        train_videos, val_videos, test_videos = self.create_speaker_disjoint_splits(video_data)
        
        # Save manifests
        self.save_manifests(train_videos, val_videos, test_videos)
        
        self.logger.info("🎉 Manifest building completed successfully!")


def main():
    parser = argparse.ArgumentParser(description='Build speaker-disjoint manifests')
    parser.add_argument('--config', default='configs/training.yaml', help='Config file path')
    args = parser.parse_args()
    
    # Load configuration
    config = load_config(args.config)
    
    # Set seed for reproducibility
    set_seed(config.get('seed', 42))
    
    # Build manifests
    builder = ManifestBuilder(config)
    builder.build_manifests()


if __name__ == "__main__":
    main()
