# ICU Lipreading Training Configuration
# Target: >80% validation accuracy on speaker-held-out 10-class problem

# Data Configuration
data:
  s3_bucket: "icudatasetphrasesfortesting"
  s3_prefix: "icu-videos/"
  target_phrases:
    - "pillow"
    - "phone" 
    - "doctor"
    - "glasses"
    - "i_need_to_move"
    - "help"
    - "my_mouth_is_dry"
    - "my_chest_hurts"
    - "my_back_hurts"
    - "i_m_hot"
  
  # Preprocessing
  img_size: 96  # Final ROI size (96x96)
  frame_length: 16  # Fixed frames per clip (16 or 32)
  fps_target: 25  # Target FPS for standardization
  
  # Splits (speaker-disjoint)
  train_ratio: 0.70
  val_ratio: 0.15
  test_ratio: 0.15
  
  # Paths
  processed_data_dir: "./data_processed"
  manifests_dir: "./manifests"

# Model Configuration
model:
  name: "Mobile3DTiny_BiGRU"
  num_classes: 10
  input_channels: 3
  dropout: 0.3
  
  # Mobile3DTiny params
  mobile3d:
    width_mult: 0.5
    depth_mult: 0.5
  
  # BiGRU params  
  bigru:
    hidden_size: 128
    num_layers: 2
    dropout: 0.2

# Training Configuration
training:
  batch_size: 32
  num_epochs: 30
  learning_rate: 0.003
  weight_decay: 1e-4
  
  # Scheduler
  scheduler:
    type: "OneCycleLR"
    max_lr: 0.003
    pct_start: 0.3
  
  # Early stopping
  early_stopping:
    patience: 5
    metric: "val_macro_f1"
    mode: "max"
  
  # Class balancing
  use_class_weights: true
  
  # Checkpointing
  save_best_only: true
  checkpoint_dir: "./checkpoints"

# Evaluation Configuration
evaluation:
  metrics:
    - "accuracy"
    - "macro_f1"
    - "per_class_precision"
    - "per_class_recall"
    - "confusion_matrix"
  
  min_class_recall: 0.70  # Minimum acceptable per-class recall

# Segmentation Configuration
segmentation:
  yolo:
    model: "yolov8n.pt"  # Lightweight model
    conf_threshold: 0.5
    classes: [0]  # Person class only
  
  sam:
    model_type: "vit_b"
    checkpoint_path: "./checkpoints/sam_vit_b.pth"
  
  # Quality filters
  quality:
    min_face_area: 1000  # Minimum face bounding box area
    min_motion_threshold: 0.1  # Minimum frame-to-frame motion
    max_blur_threshold: 100  # Maximum blur (Laplacian variance)

# Inference Configuration
inference:
  device: "auto"  # auto, cuda, cpu
  max_batch_size: 8
  timeout_seconds: 5.0
  
  # API settings
  api:
    host: "0.0.0.0"
    port: 8000
    return_top_k: 5

# Logging Configuration
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  log_dir: "./logs"
  
# Reproducibility
seed: 42
